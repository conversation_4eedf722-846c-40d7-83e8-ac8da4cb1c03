/**
 * Session Security Manager
 * 
 * Implements session security features including:
 * - Session timeout detection
 * - Concurrent session management
 * - Session fingerprinting
 * - Suspicious activity detection
 */

interface SessionFingerprint {
  userAgent: string;
  screen: string;
  timezone: string;
  language: string;
  platform: string;
}

interface SessionInfo {
  id: string;
  userId: string;
  fingerprint: SessionFingerprint;
  createdAt: number;
  lastActivity: number;
  ipAddress?: string;
  isActive: boolean;
}

interface SecurityEvent {
  type: 'session_created' | 'session_expired' | 'suspicious_activity' | 'concurrent_session';
  timestamp: number;
  details: any;
}

class SessionSecurityManager {
  private sessions: Map<string, SessionInfo> = new Map();
  private securityEvents: SecurityEvent[] = [];
  private readonly MAX_EVENTS = 100;
  private readonly SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours
  private readonly ACTIVITY_TIMEOUT = 30 * 60 * 1000; // 30 minutes
  private readonly MAX_CONCURRENT_SESSIONS = 3;

  constructor() {
    // Clean up expired sessions periodically
    setInterval(() => this.cleanupExpiredSessions(), 5 * 60 * 1000); // Every 5 minutes
    
    // Monitor for suspicious activity
    this.setupActivityMonitoring();
  }

  /**
   * Generate a unique fingerprint for the current session
   */
  generateFingerprint(): SessionFingerprint {
    const nav = navigator;
    const screen = window.screen;

    return {
      userAgent: nav.userAgent,
      screen: `${screen.width}x${screen.height}x${screen.colorDepth}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: nav.language,
      platform: nav.platform
    };
  }

  /**
   * Create a new session
   */
  createSession(userId: string, sessionId: string): SessionInfo {
    const fingerprint = this.generateFingerprint();
    const now = Date.now();

    // Check for concurrent sessions
    const userSessions = this.getUserSessions(userId);
    if (userSessions.length >= this.MAX_CONCURRENT_SESSIONS) {
      this.logSecurityEvent('concurrent_session', {
        userId,
        sessionCount: userSessions.length,
        newFingerprint: fingerprint
      });

      // Remove oldest session
      const oldestSession = userSessions.sort((a, b) => a.createdAt - b.createdAt)[0];
      this.terminateSession(oldestSession.id);
    }

    const session: SessionInfo = {
      id: sessionId,
      userId,
      fingerprint,
      createdAt: now,
      lastActivity: now,
      isActive: true
    };

    this.sessions.set(sessionId, session);
    
    this.logSecurityEvent('session_created', {
      sessionId,
      userId,
      fingerprint
    });

    return session;
  }

  /**
   * Update session activity
   */
  updateActivity(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session || !session.isActive) {
      return false;
    }

    const now = Date.now();
    
    // Check if session has expired
    if (now - session.createdAt > this.SESSION_TIMEOUT) {
      this.expireSession(sessionId);
      return false;
    }

    // Check for inactivity timeout
    if (now - session.lastActivity > this.ACTIVITY_TIMEOUT) {
      this.expireSession(sessionId);
      return false;
    }

    session.lastActivity = now;
    return true;
  }

  /**
   * Validate session fingerprint
   */
  validateFingerprint(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) return false;

    const currentFingerprint = this.generateFingerprint();
    const storedFingerprint = session.fingerprint;

    // Check for significant changes that might indicate session hijacking
    const suspiciousChanges = [
      currentFingerprint.userAgent !== storedFingerprint.userAgent,
      currentFingerprint.screen !== storedFingerprint.screen,
      currentFingerprint.timezone !== storedFingerprint.timezone,
      currentFingerprint.platform !== storedFingerprint.platform
    ];

    const changedCount = suspiciousChanges.filter(Boolean).length;

    if (changedCount >= 2) {
      this.logSecurityEvent('suspicious_activity', {
        sessionId,
        userId: session.userId,
        changes: {
          userAgent: currentFingerprint.userAgent !== storedFingerprint.userAgent,
          screen: currentFingerprint.screen !== storedFingerprint.screen,
          timezone: currentFingerprint.timezone !== storedFingerprint.timezone,
          platform: currentFingerprint.platform !== storedFingerprint.platform
        },
        currentFingerprint,
        storedFingerprint
      });

      // Terminate suspicious session
      this.terminateSession(sessionId);
      return false;
    }

    return true;
  }

  /**
   * Terminate a session
   */
  terminateSession(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.isActive = false;
    }
  }

  /**
   * Expire a session
   */
  private expireSession(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.isActive = false;
      this.logSecurityEvent('session_expired', {
        sessionId,
        userId: session.userId,
        reason: Date.now() - session.createdAt > this.SESSION_TIMEOUT ? 'timeout' : 'inactivity'
      });
    }
  }

  /**
   * Get all sessions for a user
   */
  getUserSessions(userId: string): SessionInfo[] {
    return Array.from(this.sessions.values())
      .filter(session => session.userId === userId && session.isActive);
  }

  /**
   * Clean up expired sessions
   */
  private cleanupExpiredSessions(): void {
    const now = Date.now();
    const expiredSessions: string[] = [];

    for (const [sessionId, session] of this.sessions.entries()) {
      if (!session.isActive || 
          now - session.createdAt > this.SESSION_TIMEOUT ||
          now - session.lastActivity > this.ACTIVITY_TIMEOUT) {
        expiredSessions.push(sessionId);
      }
    }

    expiredSessions.forEach(sessionId => {
      this.expireSession(sessionId);
      this.sessions.delete(sessionId);
    });
  }

  /**
   * Log security event
   */
  private logSecurityEvent(type: SecurityEvent['type'], details: any): void {
    const event: SecurityEvent = {
      type,
      timestamp: Date.now(),
      details
    };

    this.securityEvents.push(event);

    // Keep only recent events
    if (this.securityEvents.length > this.MAX_EVENTS) {
      this.securityEvents = this.securityEvents.slice(-this.MAX_EVENTS);
    }

    // Log to console in development
    if (!import.meta.env.PROD) {
      console.warn(`🔒 Security Event: ${type}`, details);
    }
  }

  /**
   * Setup activity monitoring
   */
  private setupActivityMonitoring(): void {
    // Monitor for page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        // User returned to page, update activity for all active sessions
        for (const session of this.sessions.values()) {
          if (session.isActive) {
            this.updateActivity(session.id);
          }
        }
      }
    });

    // Monitor for user interaction
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    let lastActivityUpdate = 0;
    const ACTIVITY_UPDATE_THROTTLE = 60 * 1000; // 1 minute

    const updateActivityThrottled = () => {
      const now = Date.now();
      if (now - lastActivityUpdate > ACTIVITY_UPDATE_THROTTLE) {
        lastActivityUpdate = now;
        for (const session of this.sessions.values()) {
          if (session.isActive) {
            this.updateActivity(session.id);
          }
        }
      }
    };

    activityEvents.forEach(event => {
      document.addEventListener(event, updateActivityThrottled, { passive: true });
    });
  }

  /**
   * Get security events for debugging
   */
  getSecurityEvents(): SecurityEvent[] {
    return [...this.securityEvents];
  }

  /**
   * Get session info
   */
  getSessionInfo(sessionId: string): SessionInfo | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * Check if session is valid
   */
  isSessionValid(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session || !session.isActive) return false;

    return this.updateActivity(sessionId) && this.validateFingerprint(sessionId);
  }
}

// Export singleton instance
export const sessionSecurity = new SessionSecurityManager();

// Export types
export type { SessionInfo, SessionFingerprint, SecurityEvent };
