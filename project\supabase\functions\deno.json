{"compilerOptions": {"allowJs": true, "lib": ["deno.window"], "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true}, "lint": {"rules": {"tags": ["recommended"], "include": ["ban-untagged-todo"], "exclude": ["no-unused-vars"]}}, "fmt": {"useTabs": false, "lineWidth": 80, "indentWidth": 2, "semiColons": true, "singleQuote": false, "proseWrap": "preserve"}, "tasks": {"start": "deno run --allow-net --allow-read --allow-env --watch index.ts"}, "imports": {"std/": "https://deno.land/std@0.168.0/", "supabase": "npm:@supabase/supabase-js@2", "square": "npm:square@latest"}}