import { g as globalApis } from './constants.fzPh7AOq.js';
import { V as VitestIndex } from './index.ckWaX2gY.js';
import './vi.DgezovHB.js';
import '@vitest/expect';
import '@vitest/runner';
import '@vitest/runner/utils';
import 'chai';
import './utils.C8RiOc4B.js';
import '@vitest/utils';
import './_commonjsHelpers.BFTU3MAI.js';
import '@vitest/snapshot';
import '@vitest/utils/error';
import '@vitest/utils/source-map';
import './date.W2xKR2qe.js';
import '@vitest/spy';
import './run-once.2ogXb3JV.js';
import './benchmark.Cdu9hjj4.js';
import 'expect-type';

function registerApiGlobally() {
  globalApis.forEach((api) => {
    globalThis[api] = VitestIndex[api];
  });
}

export { registerApiGlobally };
