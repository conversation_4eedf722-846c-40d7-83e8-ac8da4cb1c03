# =============================================================================
# EMPIRE PRO CLEANING BUSINESS - ENVIRONMENT CONFIGURATION
# =============================================================================
# 
# SECURITY WARNING: Never commit actual values to version control!
# Copy this file to .env and fill in your actual values.
#
# =============================================================================

# =============================================================================
# SUPABASE DATABASE CONFIGURATION
# =============================================================================
# Get these values from your Supabase project dashboard
# URL format: https://your-project-id.supabase.co
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=eyJ...your-anon-key-here

# =============================================================================
# SQUARE PAYMENT PROCESSING
# =============================================================================
# Get these from Square Developer Dashboard
# For sandbox testing:
VITE_SQUARE_APPLICATION_ID=sandbox-sq0idb-your-app-id-here
VITE_SQUARE_ACCESS_TOKEN=EAAAl-your-sandbox-access-token-here
VITE_SQUARE_LOCATION_ID=your-location-id-here
VITE_SQUARE_ENVIRONMENT=sandbox

# For production (uncomment and update when ready):
# VITE_SQUARE_APPLICATION_ID=sq0idb-your-production-app-id
# VITE_SQUARE_ACCESS_TOKEN=EAAAl-your-production-access-token
# VITE_SQUARE_ENVIRONMENT=production

# =============================================================================
# WEBHOOK CONFIGURATION
# =============================================================================
# N8N Webhook URL for booking notifications
# Format: https://your-domain.app.n8n.cloud/webhook/your-webhook-id
VITE_N8N_WEBHOOK_URL=https://your-domain.app.n8n.cloud/webhook/your-webhook-id

# =============================================================================
# SUPABASE EDGE FUNCTIONS (Server-side only)
# =============================================================================
# These are used by Supabase Edge Functions, not the frontend
# Set these in your Supabase project settings > Edge Functions
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJ...your-service-role-key-here
SQUARE_ACCESS_TOKEN=EAAAl-your-access-token-here
SQUARE_WEBHOOK_SIGNATURE_KEY=your-webhook-signature-key-here
SQUARE_ENVIRONMENT=sandbox

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Public URL for webhooks (used by ngrok or your domain)
PUBLIC_URL=https://your-domain.com

# =============================================================================
# SECURITY NOTES
# =============================================================================
# 
# 1. NEVER commit .env files to version control
# 2. Use different keys for development and production
# 3. Rotate keys regularly
# 4. Use environment-specific configurations
# 5. Validate all environment variables on startup
# 
# =============================================================================
