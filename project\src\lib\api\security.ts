/**
 * API Security Middleware
 * 
 * Provides comprehensive security measures for API calls including
 * request validation, rate limiting, CSRF protection, and security headers.
 */

import { InputSanitizer } from '../utils/validation';
import { authRateLimiter } from '../auth/rateLimiter';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../utils/errorHandler';

// Security configuration
interface SecurityConfig {
  enableRateLimit: boolean;
  enableCSRFProtection: boolean;
  enableInputValidation: boolean;
  enableSecurityHeaders: boolean;
  maxRequestSize: number;
  allowedOrigins: string[];
}

// Request context
interface RequestContext {
  method: string;
  url: string;
  headers: Record<string, string>;
  body?: any;
  userId?: string;
  userRole?: string;
  timestamp: number;
}

// Security violation types
export enum SecurityViolationType {
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  INVALID_INPUT = 'invalid_input',
  CSRF_TOKEN_MISSING = 'csrf_token_missing',
  CSRF_TOKEN_INVALID = 'csrf_token_invalid',
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  SUSPICIOUS_PAYLOAD = 'suspicious_payload',
  REQUEST_TOO_LARGE = 'request_too_large',
  INVALID_ORIGIN = 'invalid_origin'
}

// Security violation
export interface SecurityViolation {
  type: SecurityViolationType;
  message: string;
  context: RequestContext;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

class APISecurityManager {
  private config: SecurityConfig;
  private violations: SecurityViolation[] = [];
  private csrfTokens: Map<string, { token: string; expires: number }> = new Map();

  constructor(config: Partial<SecurityConfig> = {}) {
    this.config = {
      enableRateLimit: true,
      enableCSRFProtection: true,
      enableInputValidation: true,
      enableSecurityHeaders: true,
      maxRequestSize: 10 * 1024 * 1024, // 10MB
      allowedOrigins: ['http://localhost:3000', 'http://localhost:5173'],
      ...config
    };
  }

  /**
   * Validate API request security
   */
  async validateRequest(context: RequestContext): Promise<void> {
    const violations: SecurityViolation[] = [];

    // Check rate limiting
    if (this.config.enableRateLimit && context.userId) {
      try {
        if (authRateLimiter.isRateLimited(context.userId)) {
          violations.push({
            type: SecurityViolationType.RATE_LIMIT_EXCEEDED,
            message: 'Rate limit exceeded for user',
            context,
            severity: 'medium'
          });
        }
      } catch (error) {
        // Rate limit check failed, log but don't block
        console.warn('Rate limit check failed:', error);
      }
    }

    // Validate origin
    const origin = context.headers['origin'] || context.headers['referer'];
    if (origin && !this.isAllowedOrigin(origin)) {
      violations.push({
        type: SecurityViolationType.INVALID_ORIGIN,
        message: `Request from unauthorized origin: ${origin}`,
        context,
        severity: 'high'
      });
    }

    // Check request size
    if (context.body && this.getRequestSize(context.body) > this.config.maxRequestSize) {
      violations.push({
        type: SecurityViolationType.REQUEST_TOO_LARGE,
        message: 'Request payload exceeds maximum allowed size',
        context,
        severity: 'medium'
      });
    }

    // Validate CSRF token for state-changing operations
    if (this.config.enableCSRFProtection && this.isStateChangingOperation(context.method)) {
      const csrfToken = context.headers['x-csrf-token'];
      if (!csrfToken) {
        violations.push({
          type: SecurityViolationType.CSRF_TOKEN_MISSING,
          message: 'CSRF token missing for state-changing operation',
          context,
          severity: 'high'
        });
      } else if (!this.validateCSRFToken(csrfToken, context.userId)) {
        violations.push({
          type: SecurityViolationType.CSRF_TOKEN_INVALID,
          message: 'Invalid CSRF token',
          context,
          severity: 'high'
        });
      }
    }

    // Validate input
    if (this.config.enableInputValidation && context.body) {
      const inputViolations = this.validateInput(context.body);
      violations.push(...inputViolations.map(v => ({
        ...v,
        context,
        severity: 'medium' as const
      })));
    }

    // Store violations
    this.violations.push(...violations);

    // Throw error if critical violations found
    const criticalViolations = violations.filter(v => v.severity === 'critical' || v.severity === 'high');
    if (criticalViolations.length > 0) {
      throw new Error(`Security violations detected: ${criticalViolations.map(v => v.message).join(', ')}`);
    }
  }

  /**
   * Generate CSRF token
   */
  generateCSRFToken(userId: string): string {
    const token = this.generateSecureToken();
    const expires = Date.now() + (24 * 60 * 60 * 1000); // 24 hours

    this.csrfTokens.set(userId, { token, expires });
    
    // Clean up expired tokens
    this.cleanupExpiredTokens();

    return token;
  }

  /**
   * Validate CSRF token
   */
  private validateCSRFToken(token: string, userId?: string): boolean {
    if (!userId) return false;

    const storedToken = this.csrfTokens.get(userId);
    if (!storedToken) return false;

    // Check if token has expired
    if (Date.now() > storedToken.expires) {
      this.csrfTokens.delete(userId);
      return false;
    }

    return storedToken.token === token;
  }

  /**
   * Generate secure token
   */
  private generateSecureToken(): string {
    const array = new Uint8Array(32);
    if (typeof window !== 'undefined' && window.crypto) {
      window.crypto.getRandomValues(array);
    } else {
      // Fallback for environments without crypto
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
    }
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Check if operation changes state
   */
  private isStateChangingOperation(method: string): boolean {
    return ['POST', 'PUT', 'PATCH', 'DELETE'].includes(method.toUpperCase());
  }

  /**
   * Check if origin is allowed
   */
  private isAllowedOrigin(origin: string): boolean {
    // Remove trailing slash for comparison
    const normalizedOrigin = origin.replace(/\/$/, '');
    return this.config.allowedOrigins.some(allowed => 
      normalizedOrigin.startsWith(allowed.replace(/\/$/, ''))
    );
  }

  /**
   * Get request size in bytes
   */
  private getRequestSize(body: any): number {
    if (typeof body === 'string') {
      return new Blob([body]).size;
    }
    if (body instanceof FormData) {
      // Approximate size for FormData
      let size = 0;
      for (const [key, value] of body.entries()) {
        size += key.length;
        if (typeof value === 'string') {
          size += value.length;
        } else if (value instanceof File) {
          size += value.size;
        }
      }
      return size;
    }
    return new Blob([JSON.stringify(body)]).size;
  }

  /**
   * Validate input for security threats
   */
  private validateInput(input: any): Array<{
    type: SecurityViolationType;
    message: string;
  }> {
    const violations: Array<{
      type: SecurityViolationType;
      message: string;
    }> = [];

    try {
      const inputString = JSON.stringify(input);

      // Check for suspicious patterns
      const suspiciousPatterns = [
        { pattern: /<script[^>]*>.*?<\/script>/gi, name: 'Script injection' },
        { pattern: /javascript:/gi, name: 'JavaScript protocol' },
        { pattern: /vbscript:/gi, name: 'VBScript protocol' },
        { pattern: /on\w+\s*=/gi, name: 'Event handler injection' },
        { pattern: /eval\s*\(/gi, name: 'Eval function' },
        { pattern: /expression\s*\(/gi, name: 'CSS expression' },
        { pattern: /union\s+select/gi, name: 'SQL injection' },
        { pattern: /drop\s+table/gi, name: 'SQL drop table' },
        { pattern: /delete\s+from/gi, name: 'SQL delete' },
        { pattern: /../gi, name: 'Directory traversal' }
      ];

      for (const { pattern, name } of suspiciousPatterns) {
        if (pattern.test(inputString)) {
          violations.push({
            type: SecurityViolationType.SUSPICIOUS_PAYLOAD,
            message: `Suspicious pattern detected: ${name}`
          });
        }
      }

      // Recursively validate object properties
      this.validateObjectProperties(input, violations);

    } catch (error) {
      violations.push({
        type: SecurityViolationType.INVALID_INPUT,
        message: 'Failed to validate input structure'
      });
    }

    return violations;
  }

  /**
   * Recursively validate object properties
   */
  private validateObjectProperties(obj: any, violations: Array<{
    type: SecurityViolationType;
    message: string;
  }>): void {
    if (typeof obj !== 'object' || obj === null) return;

    for (const [key, value] of Object.entries(obj)) {
      // Validate property names
      if (typeof key === 'string') {
        if (key.includes('__proto__') || key.includes('constructor') || key.includes('prototype')) {
          violations.push({
            type: SecurityViolationType.SUSPICIOUS_PAYLOAD,
            message: `Suspicious property name: ${key}`
          });
        }
      }

      // Validate string values
      if (typeof value === 'string') {
        // Check for excessively long strings
        if (value.length > 10000) {
          violations.push({
            type: SecurityViolationType.SUSPICIOUS_PAYLOAD,
            message: `Excessively long string value in property: ${key}`
          });
        }

        // Sanitize and compare
        const sanitized = InputSanitizer.sanitizeText(value);
        if (sanitized !== value && sanitized.length < value.length * 0.8) {
          violations.push({
            type: SecurityViolationType.SUSPICIOUS_PAYLOAD,
            message: `Potentially malicious content in property: ${key}`
          });
        }
      }

      // Recursively validate nested objects
      if (typeof value === 'object' && value !== null) {
        this.validateObjectProperties(value, violations);
      }
    }
  }

  /**
   * Clean up expired CSRF tokens
   */
  private cleanupExpiredTokens(): void {
    const now = Date.now();
    for (const [userId, tokenData] of this.csrfTokens.entries()) {
      if (now > tokenData.expires) {
        this.csrfTokens.delete(userId);
      }
    }
  }

  /**
   * Get security headers
   */
  getSecurityHeaders(): Record<string, string> {
    if (!this.config.enableSecurityHeaders) return {};

    return {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
      'Referrer-Policy': 'strict-origin-when-cross-origin'
    };
  }

  /**
   * Get security violations for analysis
   */
  getSecurityViolations(limit: number = 100): SecurityViolation[] {
    return this.violations.slice(-limit);
  }

  /**
   * Clear security violations
   */
  clearViolations(): void {
    this.violations = [];
  }
}

// Export singleton instance
export const apiSecurity = new APISecurityManager();

// Export convenience functions
export const validateRequest = apiSecurity.validateRequest.bind(apiSecurity);
export const generateCSRFToken = apiSecurity.generateCSRFToken.bind(apiSecurity);
export const getSecurityHeaders = apiSecurity.getSecurityHeaders.bind(apiSecurity);
