import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "npm:@supabase/supabase-js";
import { Client, Environment } from "npm:square";

// TypeScript interfaces for request data
interface PaymentLinkRequest {
  amount: number;
  currency?: string;
  customerEmail?: string;
  description?: string;
  formData?: Record<string, unknown>;
}

interface SquareErrorDetails {
  message: string;
  code?: string;
  statusCode?: number;
  errors?: Array<unknown>;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
const supabaseKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// Initialize Square client with required environment variables
const squareAccessToken = Deno.env.get("SQUARE_ACCESS_TOKEN");
const squareLocationId = Deno.env.get("SQUARE_LOCATION_ID");
const squareEnvironment = Deno.env.get("SQUARE_ENVIRONMENT") === "production" 
  ? Environment.Production 
  : Environment.Sandbox;

// Validate required environment variables
if (!squareAccessToken || !squareLocationId) {
  console.error("Missing required Square configuration:", {
    hasAccessToken: !!squareAccessToken,
    hasLocationId: !!squareLocationId
  });
}

const squareClient = new Client({
  accessToken: squareAccessToken || "",
  environment: squareEnvironment,
});

// Helper function to safely serialize BigInt values
function serializeResponse(obj: unknown): unknown {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'bigint') {
    return obj.toString();
  }

  if (Array.isArray(obj)) {
    return obj.map(serializeResponse);
  }

  if (typeof obj === 'object') {
    const result: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(obj)) {
      result[key] = serializeResponse(value);
    }
    return result;
  }

  return obj;
}

serve(async (req) => {
  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };

  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers: corsHeaders,
      status: 204,
    });
  }

  // Only allow POST requests
  if (req.method !== "POST") {
    return new Response(
      JSON.stringify({ 
        error: "Method not allowed",
        success: false 
      }),
      {
        headers: { 
          ...corsHeaders,
          "Content-Type": "application/json",
        },
        status: 405,
      }
    );
  }

  try {
    // DEBUG: log raw request body to diagnose payload issues
    try {
      const raw = await req.clone().text();
      console.log("BODY_RAW", raw);
    } catch (logErr) {
      console.error("Failed to read raw body", logErr);
    }

    // Log environment variables for debugging
    console.log("Edge Function Environment:", {
      environment: squareEnvironment,
      hasAccessToken: !!squareAccessToken,
      accessTokenLength: squareAccessToken ? squareAccessToken.length : 0,
      hasLocationId: !!squareLocationId,
      locationId: squareLocationId,
      publicUrl: Deno.env.get("PUBLIC_URL") || "http://localhost:5173"
    });

    // Validate Square configuration
    if (!squareAccessToken || !squareLocationId) {
      throw new Error("Square payment system is not properly configured. Please check environment variables.");
    }

    // Parse request body
    const requestData = await req.json().catch((e) => {
      console.error("Failed to parse request body:", e);
      return null;
    }) as PaymentLinkRequest | null;
    
    if (!requestData) {
      throw new Error("Invalid request body");
    }

    console.log("Request data received:", {
      ...requestData,
      formData: requestData.formData ? "Present (not shown for brevity)" : "Missing"
    });

    const { amount, currency = "USD", customerEmail, description, formData } = requestData;

    // Validate required parameters
    if (!amount || amount <= 0) {
      throw new Error("Invalid payment amount");
    }

    // Ensure amount is an integer (Square expects amount in cents)
    const amountInCents = Math.round(Number(amount));
    if (amountInCents < 1) {
      throw new Error("Payment amount must be at least 1 cent");
    }

    // Generate a unique idempotency key
    const idempotencyKey = crypto.randomUUID();

    console.log("Creating payment link with:", {
      amount: amountInCents,
      currency,
      description,
      customerEmail: customerEmail || "Not provided",
      locationId: squareLocationId,
      idempotencyKey: idempotencyKey
    });

    // Create a payment link
    try {
      const response = await squareClient.checkoutApi.createPaymentLink({
        idempotencyKey,
        quickPay: {
          name: description || "Cleaning Service",
          priceMoney: {
            amount: BigInt(amountInCents),
            currency: currency || "USD",
          },
          locationId: squareLocationId,
        },
        checkoutOptions: {
          redirectUrl: `${Deno.env.get("PUBLIC_URL") || "http://localhost:5173"}/?payment_success=true`,
          askForShippingAddress: false,
          merchantSupportEmail: "<EMAIL>",
          ...(customerEmail && { 
            prePopulateBuyerEmail: customerEmail 
          }),
        }
      });

      if (!response?.result?.paymentLink) {
        console.error("Square API response missing payment link:", response);
        throw new Error("Failed to create payment link");
      }

      console.log("Payment link created successfully:", {
        id: response.result.paymentLink.id,
        url: response.result.paymentLink.url,
        expiresAt: response.result.paymentLink.expiresAt
      });

      // Store payment link in Supabase for tracking
      let paymentLinkDbId = null;
      try {
        const { data: paymentLinkData, error: dbError } = await supabase
          .from("payment_links")
          .insert({
            payment_link_id: response.result.paymentLink.id,
            payment_link_url: response.result.paymentLink.url,
            amount: amount / 100, // Convert from cents to dollars
            currency: currency || "USD",
            description: description,
            customer_email: customerEmail,
            status: "created",
            metadata: {
              created_at: new Date().toISOString(),
              environment: squareEnvironment,
              form_data: formData || {}
            },
          })
          .select()
          .single();

        if (dbError) {
          console.error("Error storing payment link in database:", dbError);
          // Continue even if storage fails
        } else {
          console.log("Payment link stored in database successfully");
          paymentLinkDbId = paymentLinkData?.id;
        }
      } catch (storageError) {
        console.error("Exception storing payment link in database:", storageError);
        // Continue even if storage fails
      }

      // Create corresponding payment record for webhook processing
      try {
        const { error: paymentRecordError } = await supabase
          .from("payment_records")
          .insert({
            user_id: formData?.user_id || null,
            service_type: formData?.serviceType || formData?.service_type || 'unknown',
            amount: amount / 100, // Convert from cents to dollars
            payment_link_id: response.result.paymentLink.id,
            payment_link_url: response.result.paymentLink.url,
            booking_id: formData?.booking_id || null,
            status: 'pending',
            metadata: {
              payment_link_db_id: paymentLinkDbId,
              created_via: 'payment_link',
              form_data: formData || {},
              created_at: new Date().toISOString()
            }
          });

        if (paymentRecordError) {
          console.error("Error creating payment record:", paymentRecordError);
          // Continue even if this fails - webhook will create it if needed
        } else {
          console.log("Payment record created successfully for webhook processing");
        }
      } catch (paymentRecordException) {
        console.error("Exception creating payment record:", paymentRecordException);
        // Continue even if this fails - webhook will create it if needed
      }

      // Serialize the response, handling BigInt values
      const serializedResponse = serializeResponse(response.result);

      return new Response(
        JSON.stringify({ 
          paymentLink: serializedResponse.paymentLink,
          success: true 
        }),
        {
          headers: { 
            ...corsHeaders,
            "Content-Type": "application/json",
          },
          status: 200,
        }
      );
    } catch (squareError) {
      console.error("Square API error:", squareError);
      console.error("Square API error details:", JSON.stringify({
        message: squareError.message,
        code: squareError.code,
        statusCode: squareError.statusCode,
        errors: squareError.errors
      }));
      throw new Error(`Square API Error: ${squareError.message}`);
    }
  } catch (error) {
    console.error("Error creating payment link:", error);
    
    return new Response(
      JSON.stringify({ 
        error: error.message || "Failed to create payment link",
        success: false,
        details: {
          hasAccessToken: !!squareAccessToken,
          hasLocationId: !!squareLocationId,
          environment: squareEnvironment,
          timestamp: new Date().toISOString()
        }
      }),
      {
        headers: { 
          ...corsHeaders,
          "Content-Type": "application/json",
        },
        status: 500,
      }
    );
  }
});