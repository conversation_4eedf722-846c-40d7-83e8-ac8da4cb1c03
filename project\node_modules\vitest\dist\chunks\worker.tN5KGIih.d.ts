import { File, TaskResultPack, CancelReason, Task } from '@vitest/runner';
import { ViteNodeResolveId, ModuleCacheMap } from 'vite-node';
import { S as SerializedConfig } from './config.Cy0C388Z.js';
import { T as TransformMode, U as UserConsoleLog, A as AfterSuiteRunMeta, E as Environment } from './environment.LoooBwUu.js';
import { SnapshotResult } from '@vitest/snapshot';

type ArgumentsType<T> = T extends (...args: infer A) => any ? A : never;
type ReturnType<T> = T extends (...args: any) => infer R ? R : never;
type PromisifyFn<T> = ReturnType<T> extends Promise<any> ? T : (...args: ArgumentsType<T>) => Promise<Awaited<ReturnType<T>>>;
type BirpcResolver = (name: string, resolved: (...args: unknown[]) => unknown) => ((...args: unknown[]) => unknown) | undefined;
interface ChannelOptions {
    /**
     * Function to post raw message
     */
    post: (data: any, ...extras: any[]) => any | Promise<any>;
    /**
     * Listener to receive raw message
     */
    on: (fn: (data: any, ...extras: any[]) => void) => any | Promise<any>;
    /**
     * Clear the listener when `$close` is called
     */
    off?: (fn: (data: any, ...extras: any[]) => void) => any | Promise<any>;
    /**
     * Custom function to serialize data
     *
     * by default it passes the data as-is
     */
    serialize?: (data: any) => any;
    /**
     * Custom function to deserialize data
     *
     * by default it passes the data as-is
     */
    deserialize?: (data: any) => any;
    /**
     * Call the methods with the RPC context or the original functions object
     */
    bind?: 'rpc' | 'functions';
}
interface EventOptions<Remote> {
    /**
     * Names of remote functions that do not need response.
     */
    eventNames?: (keyof Remote)[];
    /**
     * Maximum timeout for waiting for response, in milliseconds.
     *
     * @default 60_000
     */
    timeout?: number;
    /**
     * Custom resolver to resolve function to be called
     *
     * For advanced use cases only
     */
    resolver?: BirpcResolver;
    /**
     * Custom error handler
     */
    onError?: (error: Error, functionName: string, args: any[]) => boolean | void;
    /**
     * Custom error handler for timeouts
     */
    onTimeoutError?: (functionName: string, args: any[]) => boolean | void;
}
type BirpcOptions<Remote> = EventOptions<Remote> & ChannelOptions;
type BirpcFn<T> = PromisifyFn<T> & {
    /**
     * Send event without asking for response
     */
    asEvent: (...args: ArgumentsType<T>) => void;
};
type BirpcReturn<RemoteFunctions, LocalFunctions = Record<string, never>> = {
    [K in keyof RemoteFunctions]: BirpcFn<RemoteFunctions[K]>;
} & {
    $functions: LocalFunctions;
    $close: () => void;
};

interface RuntimeRPC {
    fetch: (id: string, transformMode: TransformMode) => Promise<{
        externalize?: string;
        id?: string;
    }>;
    transform: (id: string, transformMode: TransformMode) => Promise<{
        code?: string;
    }>;
    resolveId: (id: string, importer: string | undefined, transformMode: TransformMode) => Promise<{
        external?: boolean | 'absolute' | 'relative';
        id: string;
        /** @deprecated */
        meta?: Record<string, any> | null;
        /** @deprecated */
        moduleSideEffects?: boolean | 'no-treeshake' | null;
        /** @deprecated */
        syntheticNamedExports?: boolean | string | null;
    } | null>;
    /**
     * @deprecated unused
     */
    getSourceMap: (id: string, force?: boolean) => Promise<any>;
    onFinished: (files: File[], errors?: unknown[]) => void;
    onPathsCollected: (paths: string[]) => void;
    onUserConsoleLog: (log: UserConsoleLog) => void;
    onUnhandledError: (err: unknown, type: string) => void;
    onCollected: (files: File[]) => Promise<void>;
    onAfterSuiteRun: (meta: AfterSuiteRunMeta) => void;
    onTaskUpdate: (pack: TaskResultPack[]) => Promise<void>;
    onCancel: (reason: CancelReason) => void;
    getCountOfFailedTests: () => number;
    snapshotSaved: (snapshot: SnapshotResult) => void;
    resolveSnapshotPath: (testPath: string) => string;
}
interface RunnerRPC {
    onCancel: (reason: CancelReason) => void;
}

/** @deprecated unused */
type ResolveIdFunction = (id: string, importer?: string) => Promise<ViteNodeResolveId | null>;
type WorkerRPC = BirpcReturn<RuntimeRPC, RunnerRPC>;
interface ContextTestEnvironment {
    name: string;
    transformMode?: TransformMode;
    options: Record<string, any> | null;
}
interface ContextRPC {
    pool: string;
    worker: string;
    workerId: number;
    config: SerializedConfig;
    projectName: string;
    files: string[];
    environment: ContextTestEnvironment;
    providedContext: Record<string, any>;
    invalidates?: string[];
}
interface WorkerGlobalState {
    ctx: ContextRPC;
    config: SerializedConfig;
    rpc: WorkerRPC;
    current?: Task;
    filepath?: string;
    environment: Environment;
    environmentTeardownRun?: boolean;
    onCancel: Promise<CancelReason>;
    moduleCache: ModuleCacheMap;
    providedContext: Record<string, any>;
    durations: {
        environment: number;
        prepare: number;
    };
    onFilterStackTrace?: (trace: string) => string;
}

export type { BirpcOptions as B, ContextRPC as C, RuntimeRPC as R, WorkerGlobalState as W, BirpcReturn as a, WorkerRPC as b, RunnerRPC as c, ContextTestEnvironment as d, ResolveIdFunction as e };
