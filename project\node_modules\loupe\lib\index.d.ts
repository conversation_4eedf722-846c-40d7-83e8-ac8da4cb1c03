import type { Inspect, Options } from './types.js';
export declare function inspect(value: unknown, opts?: Partial<Options>): string;
export declare function registerConstructor(constructor: Function, inspector: Inspect): boolean;
export declare function registerStringTag(stringTag: string, inspector: Inspect): boolean;
export declare const custom: string | symbol;
export default inspect;
//# sourceMappingURL=index.d.ts.map