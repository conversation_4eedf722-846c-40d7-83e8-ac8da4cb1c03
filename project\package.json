{"name": "empirepro-website", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "setup": "node scripts/setup-environment.js", "env:check": "node scripts/setup-environment.js", "env:validate": "node scripts/setup-environment.js", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:security": "vitest run src/tests/security.test.ts", "test:environment": "vitest run src/tests/environment.test.ts", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "security:check": "node scripts/security-check.js", "security:audit": "npm audit && npm run security:check && npm run test:security"}, "dependencies": {"@supabase/supabase-js": "^2.39.3", "clsx": "^2.1.0", "framer-motion": "^11.0.8", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.2", "square": "^42.3.0", "tailwind-merge": "^2.2.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^6.3.5", "vitest": "^1.2.0", "@vitest/ui": "^1.2.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "jsdom": "^24.0.0"}}