# Configure Your Square Sandbox Tokens

## Step 1: Create .env.local file
Create a `.env.local` file in your project root and add your sandbox tokens:

```bash
# Replace with your actual sandbox tokens
VITE_SQUARE_APPLICATION_ID=sandbox-sq0idb-YOUR_SANDBOX_APP_ID
VITE_SQUARE_ACCESS_TOKEN=YOUR_SANDBOX_ACCESS_TOKEN
VITE_SQUARE_LOCATION_ID=YOUR_SANDBOX_LOCATION_ID
VITE_SQUARE_ENVIRONMENT=sandbox
VITE_PUBLIC_URL=http://localhost:5173
```

## Step 2: Configure Supabase Edge Function Environment Variables
In your Supabase Dashboard:
1. Go to Settings > Edge Functions
2. Add these environment variables:

```bash
SQUARE_ACCESS_TOKEN=YOUR_SANDBOX_ACCESS_TOKEN
SQUARE_LOCATION_ID=YOUR_SANDBOX_LOCATION_ID
SQUARE_ENVIRONMENT=sandbox
PUBLIC_URL=http://localhost:5173
```

## Step 3: Test Configuration
After setting up, restart your development server and test the configuration. 