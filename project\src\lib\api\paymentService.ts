import type { User } from '@supabase/supabase-js';
import { supabase } from '../supabase/client';

interface PaymentResponse {
  url: string;
  id: string;
  bookingId?: string;
}

interface ResidentialFormData {
  serviceType?: string;
  user_id?: string;
  propertyDetails?: Record<string, unknown>;
  carpetDetails?: Record<string, unknown>;
  stainTreatment?: Record<string, unknown>;
  schedule?: Record<string, unknown>;
  contact?: Record<string, unknown>;
}

// Check if Square is properly configured
export function isSquareConfigured(): boolean {
  const hasAccessToken = Boolean(import.meta.env.VITE_SQUARE_ACCESS_TOKEN);
  const hasApplicationId = Boolean(import.meta.env.VITE_SQUARE_APPLICATION_ID);
  const hasLocationId = Boolean(import.meta.env.VITE_SQUARE_LOCATION_ID);
  
  return hasAccessToken && hasApplicationId && hasLocationId;
}

export async function processResidentialPayment(
  formData: ResidentialFormData,
  amount: number,
  user: User | null
): Promise<PaymentResponse> {
  try {
    console.log('Processing residential payment:', { amount, formData });

    // Check if Square is configured
    if (!isSquareConfigured()) {
      throw new Error('Payment system is not properly configured. Please check your environment variables.');
    }

    // Check if supabase client is available
    if (!supabase) {
      throw new Error('Database client is not initialized');
    }

    // Prepare form data with user_id for the Edge Function
    const enhancedFormData = {
      ...formData,
      user_id: user?.id || null,
      serviceType: formData.serviceType || 'residential'
    };

    // Create the payment link via Edge Function (this handles both payment link and booking creation)
    const { data, error } = await supabase.functions.invoke('create-payment-link', {
      body: {
        amount: Math.round(amount * 100), // Convert dollars to cents for Square API
        description: `${formData.serviceType ? formData.serviceType.charAt(0).toUpperCase() + formData.serviceType.slice(1) : 'Residential'} Cleaning Service`,
        formData: enhancedFormData
      }
    });

    if (error) {
      console.error('Error calling create-payment-link function:', error);
      throw new Error(error.message || 'Failed to create payment link');
    }

    if (!data?.paymentLink?.url) {
      console.error('Invalid response from payment service:', data);
      throw new Error('Failed to create payment link. Please try again or contact support.');
    }

    console.log('Payment link created successfully:', data.paymentLink);
    console.log('Booking ID from Edge Function:', data.bookingId);

    return {
      url: data.paymentLink.url,
      id: data.paymentLink.id,
      bookingId: data.bookingId // This comes from the Edge Function
    };
  } catch (error) {
    console.error('Payment processing error:', error);
    
    // Provide specific error messages based on error type
    if (error instanceof Error) {
      if (error.message.includes('not properly configured')) {
        throw new Error('Payment system is currently unavailable. Please try again later or contact support.');
      }
      if (error.message.includes('Square API Error')) {
        throw new Error('Payment processing failed. Please check your payment information and try again.');
      }
      if (error.message.includes('Database client')) {
        throw new Error('Service temporarily unavailable. Please try again in a few moments.');
      }
      throw error;
    }
    
    throw new Error('An unexpected error occurred. Please try again or contact support.');
  }
}

// Check payment status
export async function checkPaymentStatus(paymentLinkId: string): Promise<string> {
  try {
    if (!supabase) {
      throw new Error('Database client is not initialized');
    }
    
    const { data, error } = await supabase.functions.invoke('check-payment-status', {
      body: { paymentLinkId }
    });
    
    if (error) throw error;
    
    return data?.status || 'unknown';
  } catch (error) {
    console.error('Error checking payment status:', error);
    throw error;
  }
}

// Update payment status
export async function updatePaymentStatus(paymentLinkId: string, status: string): Promise<boolean> {
  try {
    if (!supabase) {
      throw new Error('Database client is not initialized');
    }
    
    const { error } = await supabase
      .from('payment_records')
      .update({ status, updated_at: new Date() })
      .eq('payment_link_id', paymentLinkId);
      
    if (error) throw error;
    
    return true;
  } catch (error) {
    console.error('Error updating payment status:', error);
    throw error;
  }
}