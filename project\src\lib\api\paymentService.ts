import type { User } from '@supabase/supabase-js';
import { supabase } from '../supabase/client';
import { InputSanitizer } from '../utils/validation';
import { ErrorHandler } from '../utils/errorHandler';
import { logDatabaseOperation, AuditEventType } from '../database/auditLogger';

interface PaymentResponse {
  url: string;
  id: string;
  bookingId?: string;
}

interface ResidentialFormData {
  serviceType?: string;
  user_id?: string;
  propertyDetails?: Record<string, unknown>;
  carpetDetails?: Record<string, unknown>;
  stainTreatment?: Record<string, unknown>;
  schedule?: Record<string, unknown>;
  contact?: Record<string, unknown>;
}

// Sanitize form data to prevent XSS and injection attacks
function sanitizeFormData(formData: ResidentialFormData): ResidentialFormData {
  if (!formData || typeof formData !== 'object') {
    return {};
  }

  const sanitized: ResidentialFormData = {};

  // Sanitize string fields
  if (formData.serviceType) {
    sanitized.serviceType = InputSanitizer.sanitizeText(formData.serviceType);
  }

  if (formData.user_id) {
    sanitized.user_id = InputSanitizer.sanitizeText(formData.user_id);
  }

  // Sanitize nested objects
  if (formData.propertyDetails) {
    sanitized.propertyDetails = sanitizeObject(formData.propertyDetails);
  }

  if (formData.carpetDetails) {
    sanitized.carpetDetails = sanitizeObject(formData.carpetDetails);
  }

  if (formData.stainTreatment) {
    sanitized.stainTreatment = sanitizeObject(formData.stainTreatment);
  }

  if (formData.schedule) {
    sanitized.schedule = sanitizeObject(formData.schedule);
  }

  if (formData.contact) {
    sanitized.contact = sanitizeObject(formData.contact);
  }

  return sanitized;
}

// Helper function to sanitize nested objects
function sanitizeObject(obj: Record<string, unknown>): Record<string, unknown> {
  const sanitized: Record<string, unknown> = {};

  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      sanitized[key] = InputSanitizer.sanitizeText(value);
    } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      sanitized[key] = sanitizeObject(value as Record<string, unknown>);
    } else {
      sanitized[key] = value;
    }
  }

  return sanitized;
}

// Check if Square is properly configured
export function isSquareConfigured(): boolean {
  const hasAccessToken = Boolean(import.meta.env.VITE_SQUARE_ACCESS_TOKEN);
  const hasApplicationId = Boolean(import.meta.env.VITE_SQUARE_APPLICATION_ID);
  const hasLocationId = Boolean(import.meta.env.VITE_SQUARE_LOCATION_ID);
  
  return hasAccessToken && hasApplicationId && hasLocationId;
}

export async function processResidentialPayment(
  formData: ResidentialFormData,
  amount: number,
  user: User | null
): Promise<PaymentResponse> {
  try {
    // Basic security validation (payment endpoints have their own security)
    // Skip full security validation for payment processing as it has its own security layer

    // Input validation and sanitization
    if (!amount || amount <= 0 || amount > 10000) {
      throw new Error('Invalid payment amount. Amount must be between $0.01 and $10,000.');
    }

    if (!Number.isFinite(amount)) {
      throw new Error('Invalid payment amount format.');
    }

    // Sanitize form data
    const sanitizedFormData = sanitizeFormData(formData);

    // Log payment attempt
    logDatabaseOperation(
      AuditEventType.CREATE,
      'payment_records',
      'payment_processing_started',
      {
        userId: user?.id,
        details: {
          amount,
          serviceType: sanitizedFormData.serviceType
        },
        success: true
      }
    );

    console.log('Processing residential payment:', { amount, serviceType: sanitizedFormData.serviceType });

    // Check if Square is configured
    if (!isSquareConfigured()) {
      const error = ErrorHandler.createError(
        'PAYMENT_CONFIG_ERROR',
        'Payment system is not properly configured',
        { userId: user?.id }
      );
      ErrorHandler.logError(error);
      throw new Error('Payment system is currently unavailable. Please try again later or contact support.');
    }

    // Check if supabase client is available
    if (!supabase) {
      const error = ErrorHandler.createError(
        'DATABASE_ERROR',
        'Database client is not initialized',
        { userId: user?.id }
      );
      ErrorHandler.logError(error);
      throw new Error('Service temporarily unavailable. Please try again in a few moments.');
    }

    // Prepare form data with user_id for the Edge Function
    const enhancedFormData = {
      ...formData,
      user_id: user?.id || null,
      serviceType: formData.serviceType || 'residential'
    };

    // Create the payment link via Edge Function (this handles both payment link and booking creation)
    const { data, error } = await supabase.functions.invoke('create-payment-link', {
      body: {
        amount: Math.round(amount * 100), // Convert dollars to cents for Square API
        description: `${formData.serviceType ? formData.serviceType.charAt(0).toUpperCase() + formData.serviceType.slice(1) : 'Residential'} Cleaning Service`,
        formData: enhancedFormData
      }
    });

    if (error) {
      console.error('Error calling create-payment-link function:', error);
      throw new Error(error.message || 'Failed to create payment link');
    }

    if (!data?.paymentLink?.url) {
      console.error('Invalid response from payment service:', data);
      throw new Error('Failed to create payment link. Please try again or contact support.');
    }

    console.log('Payment link created successfully:', data.paymentLink);
    console.log('Booking ID from Edge Function:', data.bookingId);

    return {
      url: data.paymentLink.url,
      id: data.paymentLink.id,
      bookingId: data.bookingId // This comes from the Edge Function
    };
  } catch (error) {
    console.error('Payment processing error:', error);
    
    // Provide specific error messages based on error type
    if (error instanceof Error) {
      if (error.message.includes('not properly configured')) {
        throw new Error('Payment system is currently unavailable. Please try again later or contact support.');
      }
      if (error.message.includes('Square API Error')) {
        throw new Error('Payment processing failed. Please check your payment information and try again.');
      }
      if (error.message.includes('Database client')) {
        throw new Error('Service temporarily unavailable. Please try again in a few moments.');
      }
      throw error;
    }
    
    throw new Error('An unexpected error occurred. Please try again or contact support.');
  }
}

// Check payment status
export async function checkPaymentStatus(paymentLinkId: string): Promise<string> {
  try {
    // Input validation
    if (!paymentLinkId || typeof paymentLinkId !== 'string') {
      throw new Error('Invalid payment link ID');
    }

    // Sanitize input
    const sanitizedPaymentLinkId = InputSanitizer.sanitizeText(paymentLinkId);

    if (!supabase) {
      const error = ErrorHandler.createError(
        'DATABASE_ERROR',
        'Database client is not initialized'
      );
      ErrorHandler.logError(error);
      throw new Error('Service temporarily unavailable');
    }

    const { data, error } = await supabase.functions.invoke('check-payment-status', {
      body: { paymentLinkId: sanitizedPaymentLinkId }
    });

    if (error) {
      const handledError = ErrorHandler.createError(
        'PAYMENT_STATUS_ERROR',
        'Failed to check payment status',
        error
      );
      ErrorHandler.logError(handledError);
      throw new Error('Unable to check payment status');
    }

    return data?.status || 'unknown';
  } catch (error) {
    console.error('Error checking payment status:', error);
    throw error;
  }
}

// Update payment status
export async function updatePaymentStatus(paymentLinkId: string, status: string): Promise<boolean> {
  try {
    // Input validation
    if (!paymentLinkId || typeof paymentLinkId !== 'string') {
      throw new Error('Invalid payment link ID');
    }

    if (!status || typeof status !== 'string') {
      throw new Error('Invalid payment status');
    }

    // Validate status values
    const validStatuses = ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'];
    if (!validStatuses.includes(status)) {
      throw new Error('Invalid payment status value');
    }

    // Sanitize inputs
    const sanitizedPaymentLinkId = InputSanitizer.sanitizeText(paymentLinkId);
    const sanitizedStatus = InputSanitizer.sanitizeText(status);

    if (!supabase) {
      const error = ErrorHandler.createError(
        'DATABASE_ERROR',
        'Database client is not initialized'
      );
      ErrorHandler.logError(error);
      throw new Error('Service temporarily unavailable');
    }

    const { error } = await supabase
      .from('payment_records')
      .update({
        status: sanitizedStatus,
        updated_at: new Date().toISOString()
      })
      .eq('payment_link_id', sanitizedPaymentLinkId);

    if (error) {
      const handledError = ErrorHandler.createError(
        'PAYMENT_UPDATE_ERROR',
        'Failed to update payment status',
        error
      );
      ErrorHandler.logError(handledError);
      throw new Error('Unable to update payment status');
    }

    // Log successful update
    logDatabaseOperation(
      AuditEventType.UPDATE,
      'payment_records',
      'payment_status_updated',
      {
        recordId: sanitizedPaymentLinkId,
        details: {
          newStatus: sanitizedStatus
        },
        success: true
      }
    );

    return true;
  } catch (error) {
    console.error('Error updating payment status:', error);
    throw error;
  }
}