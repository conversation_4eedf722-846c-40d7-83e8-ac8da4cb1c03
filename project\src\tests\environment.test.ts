/**
 * Environment Configuration Tests
 * 
 * Tests for environment variable validation, configuration management,
 * and security checks for different deployment environments.
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { environmentValidator, validateEnvironment, getEnvironmentConfig } from '../lib/config/environment';

// Mock environment variables for testing
const mockEnvVars = {
  VITE_SUPABASE_URL: 'https://test-project.supabase.co',
  VITE_SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.key',
  VITE_SQUARE_APPLICATION_ID: 'sandbox-sq0idb-test-app-id',
  VITE_SQUARE_ACCESS_TOKEN: 'EAAAl-test-access-token-here',
  VITE_SQUARE_LOCATION_ID: 'test-location-id-123',
  VITE_SQUARE_ENVIRONMENT: 'sandbox',
  VITE_N8N_WEBHOOK_URL: 'https://test.app.n8n.cloud/webhook/test-id'
};

describe('Environment Configuration', () => {
  let originalEnv: any;

  beforeEach(() => {
    // Store original environment
    originalEnv = { ...import.meta.env };
    
    // Set test environment variables
    Object.assign(import.meta.env, mockEnvVars);
  });

  afterEach(() => {
    // Restore original environment
    Object.assign(import.meta.env, originalEnv);
  });

  describe('Environment Validation', () => {
    it('should validate complete configuration', () => {
      const validation = validateEnvironment();
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect missing Supabase URL', () => {
      delete import.meta.env.VITE_SUPABASE_URL;
      
      const validation = validateEnvironment();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors.some(error => 
        error.includes('Supabase configuration')
      )).toBe(true);
    });

    it('should detect invalid Supabase URL format', () => {
      import.meta.env.VITE_SUPABASE_URL = 'invalid-url';
      
      const validation = validateEnvironment();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors.some(error => 
        error.includes('Supabase configuration')
      )).toBe(true);
    });

    it('should detect missing Supabase anon key', () => {
      delete import.meta.env.VITE_SUPABASE_ANON_KEY;
      
      const validation = validateEnvironment();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors.some(error => 
        error.includes('Supabase configuration')
      )).toBe(true);
    });

    it('should warn about missing Square configuration in development', () => {
      delete import.meta.env.VITE_SQUARE_APPLICATION_ID;
      delete import.meta.env.VITE_SQUARE_ACCESS_TOKEN;
      delete import.meta.env.VITE_SQUARE_LOCATION_ID;
      
      const validation = validateEnvironment();
      
      // Should not be an error in development, but should be a warning
      expect(validation.warnings.some(warning => 
        warning.includes('Square payment configuration')
      )).toBe(true);
    });

    it('should require Square configuration in production', () => {
      // Mock production environment
      import.meta.env.PROD = true;
      
      delete import.meta.env.VITE_SQUARE_APPLICATION_ID;
      delete import.meta.env.VITE_SQUARE_ACCESS_TOKEN;
      delete import.meta.env.VITE_SQUARE_LOCATION_ID;
      
      const validation = validateEnvironment();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors.some(error => 
        error.includes('Square payment configuration')
      )).toBe(true);
    });

    it('should detect sandbox environment in production', () => {
      // Mock production environment
      import.meta.env.PROD = true;
      import.meta.env.VITE_SQUARE_ENVIRONMENT = 'sandbox';
      
      const validation = validateEnvironment();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors.some(error => 
        error.includes('sandbox')
      )).toBe(true);
    });
  });

  describe('Configuration Loading', () => {
    it('should load valid configuration', () => {
      const config = getEnvironmentConfig();
      
      expect(config.supabase.isConfigured).toBe(true);
      expect(config.supabase.url).toBe(mockEnvVars.VITE_SUPABASE_URL);
      expect(config.supabase.anonKey).toBe(mockEnvVars.VITE_SUPABASE_ANON_KEY);
      
      expect(config.square.isConfigured).toBe(true);
      expect(config.square.applicationId).toBe(mockEnvVars.VITE_SQUARE_APPLICATION_ID);
      expect(config.square.environment).toBe('sandbox');
      
      expect(config.webhooks.isConfigured).toBe(true);
      expect(config.webhooks.n8nUrl).toBe(mockEnvVars.VITE_N8N_WEBHOOK_URL);
    });

    it('should handle missing configuration gracefully', () => {
      // Clear all environment variables
      Object.keys(mockEnvVars).forEach(key => {
        delete import.meta.env[key];
      });
      
      const config = getEnvironmentConfig();
      
      expect(config.supabase.isConfigured).toBe(false);
      expect(config.square.isConfigured).toBe(false);
      expect(config.webhooks.isConfigured).toBe(false);
    });

    it('should detect development vs production environment', () => {
      // Test development
      import.meta.env.PROD = false;
      let config = getEnvironmentConfig();
      expect(config.app.environment).toBe('development');
      expect(config.app.isDevelopment).toBe(true);
      expect(config.app.isProduction).toBe(false);
      
      // Test production
      import.meta.env.PROD = true;
      config = getEnvironmentConfig();
      expect(config.app.environment).toBe('production');
      expect(config.app.isDevelopment).toBe(false);
      expect(config.app.isProduction).toBe(true);
    });
  });

  describe('Security Validation', () => {
    it('should validate Supabase URL format', () => {
      const testCases = [
        { url: 'https://valid-project.supabase.co', valid: true },
        { url: 'http://invalid-project.supabase.co', valid: false }, // HTTP not HTTPS
        { url: 'https://invalid-domain.com', valid: false }, // Wrong domain
        { url: 'not-a-url', valid: false }, // Invalid format
        { url: '', valid: false }, // Empty
      ];

      testCases.forEach(({ url, valid }) => {
        import.meta.env.VITE_SUPABASE_URL = url;
        const config = getEnvironmentConfig();
        expect(config.supabase.isConfigured).toBe(valid);
      });
    });

    it('should validate Supabase anon key format', () => {
      const testCases = [
        { key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.valid.key', valid: true },
        { key: 'invalid-key-format', valid: false },
        { key: 'eyJ', valid: false }, // Too short
        { key: '', valid: false }, // Empty
      ];

      testCases.forEach(({ key, valid }) => {
        import.meta.env.VITE_SUPABASE_ANON_KEY = key;
        const config = getEnvironmentConfig();
        expect(config.supabase.isConfigured).toBe(valid);
      });
    });

    it('should validate Square application ID format', () => {
      const testCases = [
        { id: 'sandbox-sq0idb-valid-app-id', valid: true },
        { id: 'sq0idb-production-app-id', valid: true },
        { id: 'invalid-app-id', valid: false },
        { id: '', valid: false },
      ];

      testCases.forEach(({ id, valid }) => {
        import.meta.env.VITE_SQUARE_APPLICATION_ID = id;
        import.meta.env.VITE_SQUARE_ACCESS_TOKEN = 'EAAAl-valid-token';
        import.meta.env.VITE_SQUARE_LOCATION_ID = 'valid-location-id';
        
        const config = getEnvironmentConfig();
        expect(config.square.isConfigured).toBe(valid);
      });
    });

    it('should validate Square access token format', () => {
      const testCases = [
        { token: 'EAAAl-valid-access-token-here', valid: true },
        { token: 'invalid-token-format', valid: false },
        { token: 'EAAAl', valid: false }, // Too short
        { token: '', valid: false },
      ];

      testCases.forEach(({ token, valid }) => {
        import.meta.env.VITE_SQUARE_APPLICATION_ID = 'sandbox-sq0idb-valid';
        import.meta.env.VITE_SQUARE_ACCESS_TOKEN = token;
        import.meta.env.VITE_SQUARE_LOCATION_ID = 'valid-location-id';
        
        const config = getEnvironmentConfig();
        expect(config.square.isConfigured).toBe(valid);
      });
    });

    it('should validate webhook URL format', () => {
      const testCases = [
        { url: 'https://valid.app.n8n.cloud/webhook/test-id', valid: true },
        { url: 'http://invalid.com/webhook/test', valid: false }, // HTTP not HTTPS
        { url: 'https://valid.com/api/endpoint', valid: true }, // Valid HTTPS
        { url: 'not-a-url', valid: false },
        { url: '', valid: false },
      ];

      testCases.forEach(({ url, valid }) => {
        import.meta.env.VITE_N8N_WEBHOOK_URL = url;
        const config = getEnvironmentConfig();
        expect(config.webhooks.isConfigured).toBe(valid);
      });
    });
  });

  describe('Configuration Summary', () => {
    it('should provide configuration summary', () => {
      const summary = environmentValidator.getConfigSummary();
      
      expect(summary).toHaveProperty('supabase');
      expect(summary).toHaveProperty('square');
      expect(summary).toHaveProperty('webhooks');
      expect(summary).toHaveProperty('app');
      
      expect(summary.supabase.configured).toBe(true);
      expect(summary.square.configured).toBe(true);
      expect(summary.webhooks.configured).toBe(true);
      expect(summary.app.environment).toBeDefined();
    });

    it('should not expose sensitive values in summary', () => {
      const summary = environmentValidator.getConfigSummary();
      const summaryString = JSON.stringify(summary);
      
      // Should not contain actual sensitive values
      expect(summaryString).not.toContain(mockEnvVars.VITE_SUPABASE_ANON_KEY);
      expect(summaryString).not.toContain(mockEnvVars.VITE_SQUARE_ACCESS_TOKEN);
      
      // Should contain status indicators
      expect(summaryString).toContain('Set');
      expect(summaryString).toContain('configured');
    });
  });
});

describe('Environment Setup Script Integration', () => {
  it('should validate environment setup requirements', () => {
    // This test ensures the environment setup script requirements
    // match the actual validation logic
    
    const requiredVars = [
      'VITE_SUPABASE_URL',
      'VITE_SUPABASE_ANON_KEY'
    ];
    
    const optionalVars = [
      'VITE_SQUARE_APPLICATION_ID',
      'VITE_SQUARE_ACCESS_TOKEN',
      'VITE_SQUARE_LOCATION_ID',
      'VITE_N8N_WEBHOOK_URL'
    ];
    
    // Test with only required vars
    Object.keys(mockEnvVars).forEach(key => {
      delete import.meta.env[key];
    });
    
    requiredVars.forEach(key => {
      import.meta.env[key] = mockEnvVars[key];
    });
    
    const validation = validateEnvironment();
    expect(validation.isValid).toBe(true);
    expect(validation.warnings.length).toBeGreaterThan(0); // Should warn about optional vars
  });
});
