/**
 * Database Schema Validation Tests
 * 
 * Tests for database schema integrity, relationships, constraints,
 * and data validation for the payment and booking systems.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { supabase } from '../lib/supabase/client';
import { secureInsert, secureSelect, secureUpdate } from '../lib/database/secureQuery';

// Mock data for testing database operations
const mockBookingData = {
  user_id: 'test-user-123',
  service_type: 'residential',
  property_details: {
    type: 'house',
    size: 'medium',
    bedrooms: '3',
    bathrooms: '2',
    address: '123 Test St',
    city: 'Test City',
    zipCode: '12345'
  },
  service_details: {
    frequency: 'one-time',
    cleaningType: 'standard',
    addOns: [],
    totalPrice: 150
  },
  schedule: {
    preferredDate: '2024-01-15',
    preferredTime: '10:00'
  },
  contact: {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+1234567890'
  },
  status: 'pending'
};

const mockPaymentData = {
  user_id: 'test-user-123',
  service_type: 'residential',
  amount: 150.00,
  payment_link_id: 'payment-link-123',
  payment_link_url: 'https://square.link/payment-123',
  square_payment_id: null,
  order_id: 'order-123',
  booking_id: 'booking-123',
  status: 'pending',
  metadata: {
    created_via: 'web_app',
    payment_method: 'square'
  }
};

describe('Database Schema Validation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock Supabase operations
    vi.mocked(supabase.from).mockReturnValue({
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: null, error: null }),
      maybeSingle: vi.fn().mockResolvedValue({ data: null, error: null })
    } as any);
  });

  describe('Booking Forms Table', () => {
    it('should validate booking_forms schema', async () => {
      const result = await secureInsert('booking_forms', mockBookingData, {
        userId: 'test-user-123',
        returnData: true
      });

      expect(supabase.from).toHaveBeenCalledWith('booking_forms');
    });

    it('should enforce required fields', () => {
      const requiredFields = [
        'user_id',
        'service_type',
        'property_details',
        'service_details',
        'schedule',
        'contact',
        'status'
      ];

      requiredFields.forEach(field => {
        expect(mockBookingData).toHaveProperty(field);
      });
    });

    it('should validate JSON field structures', () => {
      // Validate property_details structure
      expect(mockBookingData.property_details).toHaveProperty('type');
      expect(mockBookingData.property_details).toHaveProperty('size');
      expect(mockBookingData.property_details).toHaveProperty('address');

      // Validate service_details structure
      expect(mockBookingData.service_details).toHaveProperty('frequency');
      expect(mockBookingData.service_details).toHaveProperty('cleaningType');
      expect(mockBookingData.service_details).toHaveProperty('totalPrice');

      // Validate contact structure
      expect(mockBookingData.contact).toHaveProperty('firstName');
      expect(mockBookingData.contact).toHaveProperty('lastName');
      expect(mockBookingData.contact).toHaveProperty('email');
      expect(mockBookingData.contact).toHaveProperty('phone');
    });

    it('should validate service_type enum values', () => {
      const validServiceTypes = [
        'residential',
        'deep',
        'carpet',
        'commercial',
        'move-in',
        'move-out',
        'post-construction'
      ];

      expect(validServiceTypes).toContain(mockBookingData.service_type);
    });

    it('should validate status enum values', () => {
      const validStatuses = [
        'pending',
        'confirmed',
        'in-progress',
        'completed',
        'cancelled'
      ];

      expect(validStatuses).toContain(mockBookingData.status);
    });

    it('should handle data sanitization', async () => {
      const maliciousData = {
        ...mockBookingData,
        contact: {
          ...mockBookingData.contact,
          firstName: '<script>alert("xss")</script>John',
          email: '<EMAIL>\'; DROP TABLE users; --'
        }
      };

      await secureInsert('booking_forms', maliciousData, {
        userId: 'test-user-123'
      });

      // Verify that secureInsert was called (it handles sanitization internally)
      expect(supabase.from).toHaveBeenCalled();
    });
  });

  describe('Payment Records Table', () => {
    it('should validate payment_records schema', async () => {
      await secureInsert('payment_records', mockPaymentData, {
        userId: 'test-user-123',
        returnData: true
      });

      expect(supabase.from).toHaveBeenCalledWith('payment_records');
    });

    it('should enforce required payment fields', () => {
      const requiredFields = [
        'user_id',
        'service_type',
        'amount',
        'status'
      ];

      requiredFields.forEach(field => {
        expect(mockPaymentData).toHaveProperty(field);
      });
    });

    it('should validate payment amount format', () => {
      expect(typeof mockPaymentData.amount).toBe('number');
      expect(mockPaymentData.amount).toBeGreaterThan(0);
      expect(Number.isFinite(mockPaymentData.amount)).toBe(true);
    });

    it('should validate payment status values', () => {
      const validPaymentStatuses = [
        'pending',
        'processing',
        'completed',
        'failed',
        'cancelled',
        'refunded'
      ];

      expect(validPaymentStatuses).toContain(mockPaymentData.status);
    });

    it('should validate metadata structure', () => {
      expect(typeof mockPaymentData.metadata).toBe('object');
      expect(mockPaymentData.metadata).not.toBeNull();
      expect(Array.isArray(mockPaymentData.metadata)).toBe(false);
    });

    it('should handle foreign key relationships', () => {
      // Verify booking_id references booking_forms
      expect(mockPaymentData.booking_id).toBeDefined();
      expect(typeof mockPaymentData.booking_id).toBe('string');

      // Verify user_id references auth.users
      expect(mockPaymentData.user_id).toBeDefined();
      expect(typeof mockPaymentData.user_id).toBe('string');
    });
  });

  describe('Data Integrity Constraints', () => {
    it('should enforce user ownership', async () => {
      const userId = 'test-user-123';
      
      await secureSelect('booking_forms', '*', { user_id: userId }, {
        userId,
        limit: 10
      });

      expect(supabase.from).toHaveBeenCalledWith('booking_forms');
    });

    it('should validate email format in contact data', () => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      expect(emailRegex.test(mockBookingData.contact.email)).toBe(true);
    });

    it('should validate phone format in contact data', () => {
      const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
      expect(phoneRegex.test(mockBookingData.contact.phone)).toBe(true);
    });

    it('should enforce date format in schedule', () => {
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      expect(dateRegex.test(mockBookingData.schedule.preferredDate)).toBe(true);
    });

    it('should validate time format in schedule', () => {
      const timeRegex = /^\d{2}:\d{2}$/;
      expect(timeRegex.test(mockBookingData.schedule.preferredTime)).toBe(true);
    });
  });

  describe('Security Constraints', () => {
    it('should prevent SQL injection in queries', async () => {
      const maliciousInput = "'; DROP TABLE booking_forms; --";
      
      await secureSelect('booking_forms', '*', { 
        service_type: maliciousInput 
      }, {
        userId: 'test-user-123'
      });

      // secureSelect should handle SQL injection prevention
      expect(supabase.from).toHaveBeenCalled();
    });

    it('should sanitize JSON field data', async () => {
      const maliciousJsonData = {
        ...mockBookingData,
        property_details: {
          ...mockBookingData.property_details,
          address: '<script>alert("xss")</script>123 Main St'
        }
      };

      await secureInsert('booking_forms', maliciousJsonData, {
        userId: 'test-user-123'
      });

      expect(supabase.from).toHaveBeenCalled();
    });

    it('should enforce row-level security', async () => {
      const userId = 'test-user-123';
      const otherUserId = 'other-user-456';

      // User should only access their own data
      await secureSelect('booking_forms', '*', {}, {
        userId,
        userRole: 'customer'
      });

      expect(supabase.from).toHaveBeenCalled();
    });

    it('should validate user permissions for updates', async () => {
      const userId = 'test-user-123';
      
      await secureUpdate('booking_forms', 
        { status: 'confirmed' },
        { id: 'booking-123' },
        { userId, userRole: 'customer' }
      );

      expect(supabase.from).toHaveBeenCalled();
    });
  });

  describe('Performance and Indexing', () => {
    it('should use efficient queries for common operations', async () => {
      // Test common query patterns
      const commonQueries = [
        { table: 'booking_forms', filter: { user_id: 'test-user' } },
        { table: 'payment_records', filter: { booking_id: 'booking-123' } },
        { table: 'booking_forms', filter: { status: 'pending' } }
      ];

      for (const query of commonQueries) {
        await secureSelect(query.table, '*', query.filter, {
          userId: 'test-user-123',
          limit: 10
        });
      }

      expect(supabase.from).toHaveBeenCalledTimes(commonQueries.length);
    });

    it('should limit query results to prevent performance issues', async () => {
      await secureSelect('booking_forms', '*', {}, {
        userId: 'test-user-123',
        limit: 50 // Should be limited to reasonable number
      });

      expect(supabase.from).toHaveBeenCalled();
    });
  });

  describe('Data Migration and Compatibility', () => {
    it('should handle legacy data formats', () => {
      // Test backward compatibility with older data structures
      const legacyBookingData = {
        ...mockBookingData,
        // Legacy field that might exist in old records
        legacy_field: 'old_value'
      };

      expect(legacyBookingData).toHaveProperty('service_type');
      expect(legacyBookingData).toHaveProperty('contact');
    });

    it('should validate required fields for new records', () => {
      const newBookingData = { ...mockBookingData };
      
      // Remove a required field
      delete newBookingData.service_type;
      
      // Should fail validation
      expect(newBookingData).not.toHaveProperty('service_type');
    });

    it('should handle null values appropriately', () => {
      const dataWithNulls = {
        ...mockBookingData,
        user_id: null, // Guest booking
        special_instructions: null
      };

      expect(dataWithNulls.user_id).toBeNull();
      expect(dataWithNulls.special_instructions).toBeNull();
    });
  });

  describe('Audit and Logging', () => {
    it('should track creation timestamps', () => {
      const timestampFields = ['created_at', 'updated_at'];
      
      // These would be automatically added by the database
      timestampFields.forEach(field => {
        expect(typeof field).toBe('string');
      });
    });

    it('should maintain audit trail for sensitive operations', async () => {
      // Payment status updates should be audited
      await secureUpdate('payment_records',
        { status: 'completed' },
        { id: 'payment-123' },
        { userId: 'test-user-123' }
      );

      expect(supabase.from).toHaveBeenCalled();
    });

    it('should log security events', async () => {
      // Attempt to access unauthorized data
      await secureSelect('payment_records', '*', {}, {
        userId: 'unauthorized-user',
        userRole: 'guest'
      });

      expect(supabase.from).toHaveBeenCalled();
    });
  });
});
