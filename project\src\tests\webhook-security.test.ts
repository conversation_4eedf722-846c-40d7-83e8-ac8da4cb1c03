/**
 * Webhook Security Tests
 * 
 * Comprehensive tests for webhook security including signature verification,
 * payload validation, and protection against webhook-based attacks.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { createHmac } from 'crypto';

// Mock webhook data
const mockWebhookPayload = {
  type: 'payment.updated',
  data: {
    id: 'payment-123',
    order_id: 'order-123',
    status: 'COMPLETED',
    amount_money: {
      amount: 15000,
      currency: 'USD'
    },
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:05:00Z'
  }
};

const mockWebhookSecret = 'test-webhook-secret-key';

// Helper function to create HMAC signature
function createWebhookSignature(payload: string, secret: string): string {
  const hmac = createHmac('sha256', secret);
  hmac.update(payload);
  return 'sha256=' + hmac.digest('hex');
}

// Mock webhook verification function (simulating the Edge Function logic)
async function verifyWebhookSignature(body: string, signature: string, key: string): Promise<boolean> {
  if (!key || !signature) {
    return false;
  }

  try {
    const cleanSignature = signature.replace(/^sha256=/, '');
    const expectedSignature = createHmac('sha256', key).update(body).digest('hex');
    
    // Constant-time comparison
    return constantTimeCompare(cleanSignature, expectedSignature);
  } catch (error) {
    return false;
  }
}

function constantTimeCompare(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false;
  }
  
  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }
  
  return result === 0;
}

describe('Webhook Security', () => {
  let webhookBody: string;
  let validSignature: string;

  beforeEach(() => {
    webhookBody = JSON.stringify(mockWebhookPayload);
    validSignature = createWebhookSignature(webhookBody, mockWebhookSecret);
  });

  describe('Signature Verification', () => {
    it('should verify valid webhook signatures', async () => {
      const isValid = await verifyWebhookSignature(webhookBody, validSignature, mockWebhookSecret);
      expect(isValid).toBe(true);
    });

    it('should reject invalid signatures', async () => {
      const invalidSignature = 'sha256=invalid-signature';
      const isValid = await verifyWebhookSignature(webhookBody, invalidSignature, mockWebhookSecret);
      expect(isValid).toBe(false);
    });

    it('should reject missing signatures', async () => {
      const isValid = await verifyWebhookSignature(webhookBody, '', mockWebhookSecret);
      expect(isValid).toBe(false);
    });

    it('should reject missing secret key', async () => {
      const isValid = await verifyWebhookSignature(webhookBody, validSignature, '');
      expect(isValid).toBe(false);
    });

    it('should handle signature format variations', async () => {
      // Test with and without sha256= prefix
      const signatureWithPrefix = validSignature;
      const signatureWithoutPrefix = validSignature.replace('sha256=', '');

      const isValidWithPrefix = await verifyWebhookSignature(webhookBody, signatureWithPrefix, mockWebhookSecret);
      const isValidWithoutPrefix = await verifyWebhookSignature(webhookBody, signatureWithoutPrefix, mockWebhookSecret);

      expect(isValidWithPrefix).toBe(true);
      expect(isValidWithoutPrefix).toBe(true); // Our implementation handles both formats
    });

    it('should be case sensitive', async () => {
      const upperCaseSignature = validSignature.toUpperCase();
      const isValid = await verifyWebhookSignature(webhookBody, upperCaseSignature, mockWebhookSecret);
      expect(isValid).toBe(false);
    });

    it('should detect payload tampering', async () => {
      const tamperedPayload = JSON.stringify({
        ...mockWebhookPayload,
        data: {
          ...mockWebhookPayload.data,
          amount_money: {
            amount: 1000000, // Tampered amount
            currency: 'USD'
          }
        }
      });

      const isValid = await verifyWebhookSignature(tamperedPayload, validSignature, mockWebhookSecret);
      expect(isValid).toBe(false);
    });
  });

  describe('Payload Validation', () => {
    it('should validate required webhook fields', () => {
      expect(mockWebhookPayload).toHaveProperty('type');
      expect(mockWebhookPayload).toHaveProperty('data');
      expect(mockWebhookPayload.data).toHaveProperty('id');
      expect(mockWebhookPayload.data).toHaveProperty('status');
    });

    it('should validate webhook event types', () => {
      const validEventTypes = [
        'payment.created',
        'payment.updated',
        'payment.completed',
        'payment.failed'
      ];

      expect(validEventTypes).toContain(mockWebhookPayload.type);
    });

    it('should validate payment status values', () => {
      const validStatuses = [
        'PENDING',
        'PROCESSING',
        'COMPLETED',
        'FAILED',
        'CANCELED'
      ];

      expect(validStatuses).toContain(mockWebhookPayload.data.status);
    });

    it('should validate amount format', () => {
      expect(typeof mockWebhookPayload.data.amount_money.amount).toBe('number');
      expect(mockWebhookPayload.data.amount_money.amount).toBeGreaterThan(0);
      expect(Number.isInteger(mockWebhookPayload.data.amount_money.amount)).toBe(true);
    });

    it('should validate currency format', () => {
      expect(typeof mockWebhookPayload.data.amount_money.currency).toBe('string');
      expect(mockWebhookPayload.data.amount_money.currency).toBe('USD');
    });

    it('should validate timestamp formats', () => {
      const timestampRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/;
      expect(timestampRegex.test(mockWebhookPayload.data.created_at)).toBe(true);
      expect(timestampRegex.test(mockWebhookPayload.data.updated_at)).toBe(true);
    });
  });

  describe('Security Attacks Prevention', () => {
    it('should prevent replay attacks', () => {
      // In a real implementation, you would check timestamp and store processed webhook IDs
      const webhookId = mockWebhookPayload.data.id;
      const recentTimestamp = new Date(Date.now() - 60000).toISOString(); // 1 minute ago
      const timestamp = new Date(recentTimestamp).getTime();
      const currentTime = Date.now();
      const maxAge = 5 * 60 * 1000; // 5 minutes

      expect(currentTime - timestamp).toBeLessThan(maxAge);
      expect(webhookId).toBeDefined();
      expect(typeof webhookId).toBe('string');
    });

    it('should handle malformed JSON payloads', () => {
      const malformedPayloads = [
        '{"invalid": json}',
        '{"type": "payment.updated"', // Missing closing brace
        'not json at all',
        '',
        null,
        undefined
      ];

      malformedPayloads.forEach(payload => {
        expect(() => {
          if (payload) {
            JSON.parse(payload);
          } else {
            throw new Error('Invalid payload');
          }
        }).toThrow();
      });
    });

    it('should prevent XSS in webhook data', () => {
      const maliciousPayload = {
        ...mockWebhookPayload,
        data: {
          ...mockWebhookPayload.data,
          id: '<script>alert("xss")</script>payment-123'
        }
      };

      // In a real implementation, you would sanitize this data
      const sanitizedId = maliciousPayload.data.id.replace(/<script[^>]*>.*?<\/script>/gi, '');
      expect(sanitizedId).not.toContain('<script>');
      expect(sanitizedId).toBe('payment-123');
    });

    it('should prevent SQL injection in webhook data', () => {
      const maliciousPayload = {
        ...mockWebhookPayload,
        data: {
          ...mockWebhookPayload.data,
          id: "payment-123'; DROP TABLE payments; --"
        }
      };

      // In a real implementation, you would use parameterized queries
      const sanitizedId = maliciousPayload.data.id
        .replace(/[';]/g, '')
        .replace(/--/g, '')
        .replace(/DROP\s+TABLE/gi, '')
        .replace(/DELETE\s+FROM/gi, '')
        .replace(/INSERT\s+INTO/gi, '');

      expect(sanitizedId).not.toContain(';');
      expect(sanitizedId).not.toContain('--');
      expect(sanitizedId).not.toContain('DROP TABLE');
    });

    it('should handle oversized payloads', () => {
      const maxPayloadSize = 1024 * 1024; // 1MB
      const largePayload = 'x'.repeat(maxPayloadSize + 1);
      
      expect(largePayload.length).toBeGreaterThan(maxPayloadSize);
      
      // In a real implementation, you would reject oversized payloads
      const shouldReject = largePayload.length > maxPayloadSize;
      expect(shouldReject).toBe(true);
    });

    it('should validate webhook origin', () => {
      // In a real implementation, you would validate the source IP
      const allowedIPs = [
        '***********',
        '********',
        // Square's actual webhook IPs would be listed here
      ];

      const requestIP = '***********';
      expect(allowedIPs).toContain(requestIP);
    });
  });

  describe('Rate Limiting', () => {
    it('should implement webhook rate limiting', () => {
      const webhookCalls = [];
      const maxCallsPerMinute = 100;
      const timeWindow = 60 * 1000; // 1 minute

      // Simulate webhook calls
      for (let i = 0; i < 150; i++) {
        webhookCalls.push({
          timestamp: Date.now(),
          id: `webhook-${i}`
        });
      }

      // Count calls in the last minute
      const recentCalls = webhookCalls.filter(call => 
        Date.now() - call.timestamp < timeWindow
      );

      expect(recentCalls.length).toBeGreaterThan(maxCallsPerMinute);
      
      // In a real implementation, you would reject excess calls
      const shouldRateLimit = recentCalls.length > maxCallsPerMinute;
      expect(shouldRateLimit).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle webhook processing errors gracefully', async () => {
      const errorScenarios = [
        { error: 'Database connection failed', shouldRetry: true },
        { error: 'Invalid payment ID', shouldRetry: false },
        { error: 'Temporary service unavailable', shouldRetry: true },
        { error: 'Malformed webhook data', shouldRetry: false }
      ];

      errorScenarios.forEach(scenario => {
        expect(scenario.error).toBeDefined();
        expect(typeof scenario.shouldRetry).toBe('boolean');
      });
    });

    it('should implement exponential backoff for retries', () => {
      const retryAttempts = [1, 2, 3, 4, 5];
      const baseDelay = 1000; // 1 second

      const delays = retryAttempts.map(attempt => 
        Math.min(baseDelay * Math.pow(2, attempt - 1), 30000) // Max 30 seconds
      );

      expect(delays).toEqual([1000, 2000, 4000, 8000, 16000]);
    });

    it('should log webhook processing failures', () => {
      const webhookLog = {
        timestamp: new Date().toISOString(),
        webhookId: mockWebhookPayload.data.id,
        eventType: mockWebhookPayload.type,
        status: 'failed',
        error: 'Processing failed',
        retryCount: 3
      };

      expect(webhookLog.timestamp).toBeDefined();
      expect(webhookLog.webhookId).toBe(mockWebhookPayload.data.id);
      expect(webhookLog.status).toBe('failed');
    });
  });

  describe('Idempotency', () => {
    it('should handle duplicate webhook deliveries', () => {
      const processedWebhooks = new Set();
      const webhookId = mockWebhookPayload.data.id;

      // First processing
      const isFirstTime = !processedWebhooks.has(webhookId);
      if (isFirstTime) {
        processedWebhooks.add(webhookId);
      }

      // Duplicate delivery
      const isDuplicate = processedWebhooks.has(webhookId);

      expect(isFirstTime).toBe(true);
      expect(isDuplicate).toBe(true);
    });

    it('should maintain idempotency keys', () => {
      const idempotencyKey = `${mockWebhookPayload.type}-${mockWebhookPayload.data.id}-${mockWebhookPayload.data.updated_at}`;
      
      expect(idempotencyKey).toBeDefined();
      expect(typeof idempotencyKey).toBe('string');
      expect(idempotencyKey.length).toBeGreaterThan(0);
    });
  });
});
