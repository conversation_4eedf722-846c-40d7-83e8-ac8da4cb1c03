export { g as getCoverageProvider, s as startCoverageInsideWorker, a as stopCoverageInsideWorker, t as takeCoverageInsideWorker } from './chunks/coverage.BoMDb1ip.js';
export { s as SpyModule } from './chunks/spy.Cf_4R5Oe.js';
export { l as loadDiffConfig, a as loadSnapshotSerializers, s as setupCommonEnv } from './chunks/setup-common.Dj6BZI3u.js';
export { collectTests, processError, startTests } from '@vitest/runner';
import '@vitest/spy';
import '@vitest/snapshot';
import '@vitest/utils';
import './chunks/run-once.2ogXb3JV.js';
import './chunks/utils.C8RiOc4B.js';
