# Square Payment System Testing & Deployment Guide

## ✅ **Prerequisites Checklist**

Before testing, ensure you have:
- [ ] Square Developer Account created
- [ ] Sandbox Application created in Square Developer Dashboard
- [ ] Sandbox API credentials (Application ID, Access Token, Location ID)
- [ ] Supabase project with Edge Functions enabled
- [ ] Database migrations applied (payment_records, payment_links tables)

## 🔧 **Step 1: Configure Environment Variables**

### Frontend (.env.local)
```bash
# Square Sandbox Configuration
VITE_SQUARE_APPLICATION_ID=sandbox-sq0idb-YOUR_SANDBOX_APP_ID
VITE_SQUARE_ACCESS_TOKEN=YOUR_SANDBOX_ACCESS_TOKEN
VITE_SQUARE_LOCATION_ID=YOUR_SANDBOX_LOCATION_ID
VITE_SQUARE_ENVIRONMENT=sandbox
VITE_PUBLIC_URL=http://localhost:5173
```

### Supabase Edge Functions
In Supabase Dashboard → Settings → Edge Functions:
```bash
SQUARE_ACCESS_TOKEN=YOUR_SANDBOX_ACCESS_TOKEN
SQUARE_LOCATION_ID=YOUR_SANDBOX_LOCATION_ID
SQUARE_ENVIRONMENT=sandbox
PUBLIC_URL=http://localhost:5173
```

## 🧪 **Step 2: Test Configuration**

### 2.1 Use Configuration Validation Component
```typescript
// Add to your development routes
import { PaymentConfigStatus } from './components/debug/PaymentConfigStatus';

// Route: /debug/payment-config
<Route path="/debug/payment-config" element={<PaymentConfigStatus />} />
```

### 2.2 Manual Configuration Check
```typescript
// Test in browser console
import { validateSquareConfig } from './lib/square/config';
import { isSquareConfigured } from './lib/api/paymentService';

console.log('Square Config:', validateSquareConfig());
console.log('Is Configured:', isSquareConfigured());
```

## 🛠️ **Step 3: Test Payment Flow**

### 3.1 Test Residential Service Payment
1. Navigate to residential service form
2. Fill out form with test data:
   - Use fake but valid email format
   - Use realistic square footage (e.g., 1500 sq ft)
   - Select appropriate options
3. Proceed to payment
4. Verify payment modal opens with correct amount
5. Click "Continue to Payment"
6. Verify redirect to Square payment page

### 3.2 Test Payment Processing
1. Use Square's test card numbers:
   - **Success:** 4111 1111 1111 1111
   - **Decline:** 4000 0000 0000 0002
   - **Insufficient Funds:** 4000 0000 0000 9995
2. Enter future expiration date and any CVV
3. Complete payment
4. Verify redirect back to your application

### 3.3 Test Database Updates
```sql
-- Check payment records were created
SELECT * FROM payment_records ORDER BY created_at DESC LIMIT 5;

-- Check payment links
SELECT * FROM payment_links ORDER BY created_at DESC LIMIT 5;

-- Check booking forms
SELECT * FROM booking_forms ORDER BY created_at DESC LIMIT 5;
```

## 🔍 **Step 4: Test Error Handling**

### 4.1 Test Configuration Errors
1. Temporarily remove environment variables
2. Restart development server
3. Try to create payment - should show proper error message
4. Restore environment variables

### 4.2 Test Payment Failures
1. Use declined test card (4000 0000 0000 0002)
2. Verify proper error handling
3. Check database for failed payment record

### 4.3 Test Network Issues
1. Disconnect internet temporarily
2. Try to process payment
3. Verify graceful error handling

## 📊 **Step 5: Monitor and Debug**

### 5.1 Development Monitoring
```typescript
// Add to your development environment
console.log('Payment Debug Info:', {
  squareConfig: validateSquareConfig(),
  environment: import.meta.env.VITE_SQUARE_ENVIRONMENT,
  hasCredentials: isSquareConfigured()
});
```

### 5.2 Check Edge Function Logs
1. Go to Supabase Dashboard → Edge Functions
2. Select `create-payment-link` function
3. View logs for any errors
4. Check webhook function logs

### 5.3 Database Monitoring
```sql
-- Monitor payment processing
SELECT 
  status,
  COUNT(*) as count,
  AVG(amount) as avg_amount
FROM payment_records 
WHERE created_at >= NOW() - INTERVAL '1 day'
GROUP BY status;
```

## 🚀 **Step 6: Production Deployment**

### 6.1 Create Production Square Application
1. Go to Square Developer Dashboard
2. Create new Production application
3. Get production credentials
4. Set up production webhook endpoints

### 6.2 Update Environment Variables
```bash
# Production Environment
VITE_SQUARE_APPLICATION_ID=sq0idb-YOUR_PRODUCTION_APP_ID
VITE_SQUARE_ACCESS_TOKEN=YOUR_PRODUCTION_ACCESS_TOKEN
VITE_SQUARE_LOCATION_ID=YOUR_PRODUCTION_LOCATION_ID
VITE_SQUARE_ENVIRONMENT=production
VITE_PUBLIC_URL=https://your-domain.com
```

### 6.3 Configure Production Webhooks
1. In Square Developer Dashboard → Webhooks
2. Add webhook URL: `https://your-domain.com/api/webhook-payment-status`
3. Subscribe to events:
   - `payment.created`
   - `payment.updated`
4. Set webhook signature key in Supabase environment

### 6.4 Test Production Environment
1. Use real payment methods (start with small amounts)
2. Test full payment flow
3. Verify webhook processing
4. Monitor logs and error rates

## 🔐 **Step 7: Security Considerations**

### 7.1 Environment Security
- [ ] Never commit API keys to version control
- [ ] Use different keys for development/production
- [ ] Rotate API keys regularly
- [ ] Enable webhook signature verification

### 7.2 Database Security
- [ ] Enable Row Level Security (RLS)
- [ ] Audit payment data access
- [ ] Set up proper user permissions
- [ ] Regular security audits

## 📈 **Step 8: Performance Optimization**

### 8.1 Frontend Optimization
```typescript
// Lazy load payment components
const PaymentModal = lazy(() => import('./components/PaymentModal'));

// Use React.memo for payment forms
const PaymentForm = React.memo(({ amount, onSubmit }) => {
  // Component logic
});
```

### 8.2 Backend Optimization
- Enable database indexing on payment tables
- Monitor Edge Function performance
- Set up caching for frequent queries
- Implement rate limiting

## 🚨 **Troubleshooting Common Issues**

### Issue: "Payment system is not properly configured"
**Solution:** Check environment variables and API credentials

### Issue: "Invalid payment amount"
**Solution:** Verify amount conversion (dollars to cents)

### Issue: "Square API Error"
**Solution:** Check API credentials and network connectivity

### Issue: Database connection errors
**Solution:** Verify Supabase connection and table permissions

### Issue: Webhook not processing
**Solution:** Check webhook URL, signature verification, and logs

## 📋 **Final Verification Checklist**

- [ ] All environment variables configured
- [ ] Configuration validation passes
- [ ] Test payments work with sandbox
- [ ] Database updates correctly
- [ ] Error handling works properly
- [ ] Webhooks process correctly
- [ ] Production credentials ready
- [ ] Security measures implemented
- [ ] Performance optimized
- [ ] Monitoring and logging active

## 🎯 **Success Criteria**

Your Square payment system is ready for production when:
1. ✅ All configuration checks pass
2. ✅ Test payments complete successfully
3. ✅ Database updates work correctly
4. ✅ Error handling is robust
5. ✅ Webhooks process payment updates
6. ✅ Security measures are in place
7. ✅ Performance is optimized
8. ✅ Monitoring is active

**Congratulations! Your Square payment system for residential services is now fully operational!** 🎉 