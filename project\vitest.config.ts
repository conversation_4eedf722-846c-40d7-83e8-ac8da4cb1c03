/// <reference types="vitest" />
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/tests/setup.ts'],
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    exclude: ['node_modules', 'dist', '.idea', '.git', '.cache'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/tests/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    // Security-focused test configuration
    testTimeout: 10000,
    hookTimeout: 10000,
    teardownTimeout: 5000,
    // Isolate tests to prevent data leakage
    isolate: true,
    // Pool options for better security isolation
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        isolate: true
      }
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@tests': path.resolve(__dirname, './src/tests')
    }
  },
  // Environment variables for testing
  define: {
    'import.meta.env.VITE_SUPABASE_URL': JSON.stringify('https://test-project.supabase.co'),
    'import.meta.env.VITE_SUPABASE_ANON_KEY': JSON.stringify('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.key'),
    'import.meta.env.VITE_SQUARE_APPLICATION_ID': JSON.stringify('sandbox-sq0idb-test'),
    'import.meta.env.VITE_SQUARE_ACCESS_TOKEN': JSON.stringify('EAAAl-test-token'),
    'import.meta.env.VITE_SQUARE_LOCATION_ID': JSON.stringify('test-location'),
    'import.meta.env.VITE_SQUARE_ENVIRONMENT': JSON.stringify('sandbox'),
    'import.meta.env.VITE_N8N_WEBHOOK_URL': JSON.stringify('https://test.n8n.cloud/webhook/test'),
    'import.meta.env.PROD': false,
    'import.meta.env.DEV': true,
    'import.meta.env.MODE': JSON.stringify('test')
  }
});
