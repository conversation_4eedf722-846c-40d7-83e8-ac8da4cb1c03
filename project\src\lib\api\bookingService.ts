import { secureInsert } from '../database/secureQuery';
import { <PERSON>rrorHandler } from '../utils/errorHandler';

export interface BookingData {
  id?: string;
  user_id?: string | null;
  service_type: string;
  property_details: Record<string, unknown>;
  service_details: Record<string, unknown>;
  schedule: Record<string, unknown>;
  contact: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    [key: string]: string;
  };
  status?: string;
  total_price?: number;
  special_instructions?: string;
  metadata?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
}

export const saveBookingToDatabase = async (bookingData: BookingData, userId?: string) => {
  try {
    const finalBookingData = {
      ...bookingData,
      status: bookingData.status || 'pending',
      metadata: {
        submittedAt: new Date().toISOString(),
        requestType: 'booking',
        ...bookingData.metadata,
      }
    };

    // Use secure insert with user context
    const data = await secureInsert(
      'booking_forms',
      finalBookingData,
      {
        userId,
        returnData: true
      }
    ) as unknown as BookingData[];

    if (!data || (Array.isArray(data) && data.length === 0)) {
      throw new Error('Failed to save booking - no data returned');
    }

    const savedBooking = Array.isArray(data) ? data[0] : data;

    ErrorHandler.logSuccess('Booking saved successfully', {
      bookingId: savedBooking.id,
      serviceType: bookingData.service_type,
      userId
    });

    return savedBooking;
  } catch (error) {
    ErrorHandler.logError(ErrorHandler.createError(
      'BOOKING_SAVE_ERROR',
      'Failed to save booking to database',
      error
    ));
    throw error;
  }
};