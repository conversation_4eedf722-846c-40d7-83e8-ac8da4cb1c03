#!/usr/bin/env node

/**
 * Security Verification Script
 * 
 * Comprehensive security check for the Empire Pro Cleaning Business application.
 * Verifies that all security measures are properly implemented and configured.
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(message.toUpperCase(), 'bold');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Security checks
const securityChecks = {
  // Check for hardcoded secrets
  checkHardcodedSecrets: () => {
    logInfo('Checking for hardcoded secrets...');
    const issues = [];
    
    const secretPatterns = [
      /password\s*[:=]\s*["'][^"']{3,}["']/gi,
      /secret\s*[:=]\s*["'][^"']{3,}["']/gi,
      /token\s*[:=]\s*["'][^"']{10,}["']/gi,
      /api[_-]?key\s*[:=]\s*["'][^"']{10,}["']/gi,
      /access[_-]?token\s*[:=]\s*["'][^"']{10,}["']/gi,
      /eyJ[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*/g, // JWT tokens
    ];
    
    const excludePatterns = [
      /\.test\./,
      /\.spec\./,
      /test/,
      /mock/,
      /example/,
      /placeholder/,
      /your-actual/i,
      /\[REDACTED\]/,
      /\[JWT_TOKEN_REDACTED\]/
    ];
    
    function scanFile(filePath) {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        secretPatterns.forEach((pattern, index) => {
          const matches = content.match(pattern);
          if (matches) {
            matches.forEach(match => {
              // Skip if it's in excluded patterns (test files, examples, etc.)
              const isExcluded = excludePatterns.some(excludePattern => 
                excludePattern.test(filePath) || excludePattern.test(match)
              );
              
              if (!isExcluded) {
                issues.push({
                  file: filePath,
                  pattern: pattern.toString(),
                  match: match.substring(0, 50) + '...',
                  line: content.substring(0, content.indexOf(match)).split('\n').length
                });
              }
            });
          }
        });
      } catch (error) {
        // Skip files that can't be read
      }
    }
    
    function scanDirectory(dir) {
      try {
        const items = fs.readdirSync(dir);
        
        items.forEach(item => {
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory()) {
            // Skip node_modules, .git, dist, etc.
            if (!['node_modules', '.git', 'dist', 'build', '.next'].includes(item)) {
              scanDirectory(fullPath);
            }
          } else if (stat.isFile()) {
            // Only scan relevant file types
            const ext = path.extname(item).toLowerCase();
            if (['.js', '.ts', '.jsx', '.tsx', '.json', '.env'].includes(ext)) {
              scanFile(fullPath);
            }
          }
        });
      } catch (error) {
        // Skip directories that can't be read
      }
    }
    
    scanDirectory(process.cwd());
    
    if (issues.length === 0) {
      logSuccess('No hardcoded secrets found');
    } else {
      logError(`Found ${issues.length} potential hardcoded secrets:`);
      issues.forEach(issue => {
        log(`  📁 ${issue.file}:${issue.line}`, 'red');
        log(`     Pattern: ${issue.pattern}`, 'yellow');
        log(`     Match: ${issue.match}`, 'yellow');
      });
    }
    
    return issues.length === 0;
  },

  // Check environment configuration
  checkEnvironmentConfig: () => {
    logInfo('Checking environment configuration...');
    const issues = [];
    
    // Check .env.example exists
    const envExamplePath = path.join(process.cwd(), '.env.example');
    if (!fs.existsSync(envExamplePath)) {
      issues.push('.env.example file is missing');
    } else {
      logSuccess('.env.example file exists');
    }
    
    // Check .env is in .gitignore
    const gitignorePath = path.join(process.cwd(), '.gitignore');
    if (fs.existsSync(gitignorePath)) {
      const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
      if (!gitignoreContent.includes('.env')) {
        issues.push('.env files are not in .gitignore');
      } else {
        logSuccess('.env files are properly ignored by git');
      }
    } else {
      issues.push('.gitignore file is missing');
    }
    
    // Check for environment validation
    const envValidationPath = path.join(process.cwd(), 'src', 'lib', 'config', 'environment.ts');
    if (!fs.existsSync(envValidationPath)) {
      issues.push('Environment validation module is missing');
    } else {
      logSuccess('Environment validation module exists');
    }
    
    if (issues.length === 0) {
      logSuccess('Environment configuration is secure');
    } else {
      logError('Environment configuration issues:');
      issues.forEach(issue => log(`  • ${issue}`, 'red'));
    }
    
    return issues.length === 0;
  },

  // Check security modules
  checkSecurityModules: () => {
    logInfo('Checking security modules...');
    const issues = [];
    
    const requiredModules = [
      'src/lib/utils/validation.ts',
      'src/lib/auth/rateLimiter.ts',
      'src/lib/api/security.ts',
      'src/lib/auth/rbac.ts',
      'src/lib/database/secureQuery.ts',
      'src/lib/database/auditLogger.ts'
    ];
    
    requiredModules.forEach(modulePath => {
      const fullPath = path.join(process.cwd(), modulePath);
      if (!fs.existsSync(fullPath)) {
        issues.push(`Security module missing: ${modulePath}`);
      } else {
        logSuccess(`Security module exists: ${modulePath}`);
      }
    });
    
    if (issues.length === 0) {
      logSuccess('All security modules are present');
    } else {
      logError('Missing security modules:');
      issues.forEach(issue => log(`  • ${issue}`, 'red'));
    }
    
    return issues.length === 0;
  },

  // Check for security tests
  checkSecurityTests: () => {
    logInfo('Checking security tests...');
    const issues = [];
    
    const testFiles = [
      'src/tests/security.test.ts',
      'src/tests/environment.test.ts'
    ];
    
    testFiles.forEach(testFile => {
      const fullPath = path.join(process.cwd(), testFile);
      if (!fs.existsSync(fullPath)) {
        issues.push(`Security test missing: ${testFile}`);
      } else {
        logSuccess(`Security test exists: ${testFile}`);
      }
    });
    
    // Check if vitest is configured
    const vitestConfigPath = path.join(process.cwd(), 'vitest.config.ts');
    if (!fs.existsSync(vitestConfigPath)) {
      issues.push('Vitest configuration is missing');
    } else {
      logSuccess('Vitest configuration exists');
    }
    
    if (issues.length === 0) {
      logSuccess('Security tests are properly configured');
    } else {
      logError('Security test issues:');
      issues.forEach(issue => log(`  • ${issue}`, 'red'));
    }
    
    return issues.length === 0;
  },

  // Check package.json for security
  checkPackageSecurity: () => {
    logInfo('Checking package.json security...');
    const issues = [];
    
    const packagePath = path.join(process.cwd(), 'package.json');
    if (!fs.existsSync(packagePath)) {
      issues.push('package.json is missing');
      return false;
    }
    
    try {
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      
      // Check for security-related scripts
      const securityScripts = ['test:security', 'test:coverage'];
      securityScripts.forEach(script => {
        if (!packageJson.scripts || !packageJson.scripts[script]) {
          issues.push(`Security script missing: ${script}`);
        } else {
          logSuccess(`Security script exists: ${script}`);
        }
      });
      
      // Check for testing dependencies
      const testDeps = ['vitest', '@testing-library/react', 'jsdom'];
      testDeps.forEach(dep => {
        if (!packageJson.devDependencies || !packageJson.devDependencies[dep]) {
          issues.push(`Testing dependency missing: ${dep}`);
        } else {
          logSuccess(`Testing dependency exists: ${dep}`);
        }
      });
      
    } catch (error) {
      issues.push('package.json is not valid JSON');
    }
    
    if (issues.length === 0) {
      logSuccess('Package.json security configuration is good');
    } else {
      logError('Package.json security issues:');
      issues.forEach(issue => log(`  • ${issue}`, 'red'));
    }
    
    return issues.length === 0;
  }
};

// Main security check function
function runSecurityCheck() {
  logHeader('Empire Pro Cleaning - Security Verification');
  
  const results = {};
  let overallPass = true;
  
  // Run all security checks
  Object.entries(securityChecks).forEach(([checkName, checkFunction]) => {
    try {
      const result = checkFunction();
      results[checkName] = result;
      if (!result) {
        overallPass = false;
      }
    } catch (error) {
      logError(`Error running ${checkName}: ${error.message}`);
      results[checkName] = false;
      overallPass = false;
    }
  });
  
  // Summary
  logHeader('Security Check Summary');
  
  Object.entries(results).forEach(([checkName, passed]) => {
    const displayName = checkName.replace(/([A-Z])/g, ' $1').toLowerCase();
    if (passed) {
      logSuccess(`${displayName}: PASSED`);
    } else {
      logError(`${displayName}: FAILED`);
    }
  });
  
  log('\n');
  if (overallPass) {
    logSuccess('🎉 All security checks passed!');
    logInfo('Your application has strong security measures in place.');
  } else {
    logError('❌ Some security checks failed.');
    logWarning('Please address the issues above before deploying to production.');
  }
  
  log('\n');
  logInfo('Next steps:');
  logInfo('1. Run: npm run test:security');
  logInfo('2. Run: npm run test:coverage');
  logInfo('3. Review and fix any remaining issues');
  logInfo('4. Consider running additional security audits');
  
  return overallPass;
}

// Run the security check
if (require.main === module) {
  const passed = runSecurityCheck();
  process.exit(passed ? 0 : 1);
}

module.exports = { runSecurityCheck, securityChecks };
