/**
 * Database Audit Logger
 * 
 * Provides comprehensive audit logging for database operations
 * to track changes, detect suspicious activity, and maintain
 * compliance with security requirements.
 */

import { <PERSON><PERSON>r<PERSON>and<PERSON> } from '../utils/errorHandler';

// Audit event types
export enum AuditEventType {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  LOGIN = 'login',
  LOGOUT = 'logout',
  PERMISSION_DENIED = 'permission_denied',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity'
}

// Audit severity levels
export enum AuditSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Audit log entry interface
export interface AuditLogEntry {
  id: string;
  timestamp: string;
  eventType: AuditEventType;
  severity: AuditSeverity;
  userId?: string;
  userEmail?: string;
  userRole?: string;
  table?: string;
  recordId?: string;
  action: string;
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  success: boolean;
  errorMessage?: string;
}

class DatabaseAuditLogger {
  private logs: AuditLogEntry[] = [];
  private readonly MAX_LOGS = 10000;
  private readonly RETENTION_DAYS = 90;

  constructor() {
    // Clean up old logs periodically
    setInterval(() => this.cleanupOldLogs(), 24 * 60 * 60 * 1000); // Daily
  }

  /**
   * Log a database operation
   */
  logDatabaseOperation(
    eventType: AuditEventType,
    table: string,
    action: string,
    options: {
      userId?: string;
      userEmail?: string;
      userRole?: string;
      recordId?: string;
      details?: Record<string, any>;
      success?: boolean;
      errorMessage?: string;
      severity?: AuditSeverity;
    } = {}
  ): void {
    const entry: AuditLogEntry = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      eventType,
      severity: options.severity || this.determineSeverity(eventType, options.success !== false),
      userId: options.userId,
      userEmail: options.userEmail,
      userRole: options.userRole,
      table,
      recordId: options.recordId,
      action,
      details: options.details || {},
      ipAddress: this.getClientIP(),
      userAgent: this.getUserAgent(),
      sessionId: this.getSessionId(),
      success: options.success !== false,
      errorMessage: options.errorMessage
    };

    this.addLogEntry(entry);
  }

  /**
   * Log authentication events
   */
  logAuthEvent(
    eventType: AuditEventType.LOGIN | AuditEventType.LOGOUT,
    options: {
      userId?: string;
      userEmail?: string;
      success?: boolean;
      errorMessage?: string;
      details?: Record<string, any>;
    } = {}
  ): void {
    const entry: AuditLogEntry = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      eventType,
      severity: options.success !== false ? AuditSeverity.LOW : AuditSeverity.MEDIUM,
      userId: options.userId,
      userEmail: options.userEmail,
      action: eventType === AuditEventType.LOGIN ? 'user_login' : 'user_logout',
      details: options.details || {},
      ipAddress: this.getClientIP(),
      userAgent: this.getUserAgent(),
      sessionId: this.getSessionId(),
      success: options.success !== false,
      errorMessage: options.errorMessage
    };

    this.addLogEntry(entry);
  }

  /**
   * Log security events
   */
  logSecurityEvent(
    eventType: AuditEventType.PERMISSION_DENIED | AuditEventType.SUSPICIOUS_ACTIVITY,
    action: string,
    options: {
      userId?: string;
      userEmail?: string;
      userRole?: string;
      table?: string;
      recordId?: string;
      details?: Record<string, any>;
      severity?: AuditSeverity;
    } = {}
  ): void {
    const entry: AuditLogEntry = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      eventType,
      severity: options.severity || AuditSeverity.HIGH,
      userId: options.userId,
      userEmail: options.userEmail,
      userRole: options.userRole,
      table: options.table,
      recordId: options.recordId,
      action,
      details: options.details || {},
      ipAddress: this.getClientIP(),
      userAgent: this.getUserAgent(),
      sessionId: this.getSessionId(),
      success: false
    };

    this.addLogEntry(entry);

    // Log security events to console in development
    if (!import.meta.env.PROD) {
      console.warn('🔒 Security Event Logged:', entry);
    }
  }

  /**
   * Add log entry to storage
   */
  private addLogEntry(entry: AuditLogEntry): void {
    this.logs.push(entry);

    // Maintain log size limit
    if (this.logs.length > this.MAX_LOGS) {
      this.logs = this.logs.slice(-this.MAX_LOGS);
    }

    // Log to console in development
    if (!import.meta.env.PROD) {
      console.log('📝 Audit Log:', entry);
    }

    // In production, you would send this to a secure logging service
    this.sendToSecureLogging(entry);
  }

  /**
   * Determine severity based on event type and success
   */
  private determineSeverity(eventType: AuditEventType, success: boolean): AuditSeverity {
    if (!success) {
      switch (eventType) {
        case AuditEventType.DELETE:
          return AuditSeverity.HIGH;
        case AuditEventType.UPDATE:
          return AuditSeverity.MEDIUM;
        default:
          return AuditSeverity.MEDIUM;
      }
    }

    switch (eventType) {
      case AuditEventType.DELETE:
        return AuditSeverity.MEDIUM;
      case AuditEventType.UPDATE:
        return AuditSeverity.LOW;
      case AuditEventType.CREATE:
        return AuditSeverity.LOW;
      case AuditEventType.READ:
        return AuditSeverity.LOW;
      default:
        return AuditSeverity.LOW;
    }
  }

  /**
   * Generate unique ID for log entry
   */
  private generateId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get client IP address (simplified for client-side)
   */
  private getClientIP(): string {
    // In a real application, this would be determined server-side
    return 'client-side';
  }

  /**
   * Get user agent
   */
  private getUserAgent(): string {
    return typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown';
  }

  /**
   * Get session ID
   */
  private getSessionId(): string {
    // In a real application, this would come from session management
    return sessionStorage.getItem('sessionId') || 'unknown';
  }

  /**
   * Send log entry to secure logging service
   */
  private sendToSecureLogging(entry: AuditLogEntry): void {
    // In production, implement secure logging to external service
    // For now, store in localStorage for development
    try {
      const existingLogs = JSON.parse(localStorage.getItem('auditLogs') || '[]');
      existingLogs.push(entry);
      
      // Keep only recent logs in localStorage
      const recentLogs = existingLogs.slice(-1000);
      localStorage.setItem('auditLogs', JSON.stringify(recentLogs));
    } catch (error) {
      console.error('Failed to store audit log:', error);
    }
  }

  /**
   * Clean up old logs
   */
  private cleanupOldLogs(): void {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.RETENTION_DAYS);
    const cutoffTimestamp = cutoffDate.toISOString();

    this.logs = this.logs.filter(log => log.timestamp > cutoffTimestamp);

    // Also clean up localStorage
    try {
      const existingLogs = JSON.parse(localStorage.getItem('auditLogs') || '[]');
      const filteredLogs = existingLogs.filter((log: AuditLogEntry) => 
        log.timestamp > cutoffTimestamp
      );
      localStorage.setItem('auditLogs', JSON.stringify(filteredLogs));
    } catch (error) {
      console.error('Failed to cleanup audit logs:', error);
    }
  }

  /**
   * Get audit logs for analysis
   */
  getAuditLogs(filters: {
    eventType?: AuditEventType;
    severity?: AuditSeverity;
    userId?: string;
    table?: string;
    startDate?: string;
    endDate?: string;
    limit?: number;
  } = {}): AuditLogEntry[] {
    let filteredLogs = [...this.logs];

    // Apply filters
    if (filters.eventType) {
      filteredLogs = filteredLogs.filter(log => log.eventType === filters.eventType);
    }

    if (filters.severity) {
      filteredLogs = filteredLogs.filter(log => log.severity === filters.severity);
    }

    if (filters.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === filters.userId);
    }

    if (filters.table) {
      filteredLogs = filteredLogs.filter(log => log.table === filters.table);
    }

    if (filters.startDate) {
      filteredLogs = filteredLogs.filter(log => log.timestamp >= filters.startDate!);
    }

    if (filters.endDate) {
      filteredLogs = filteredLogs.filter(log => log.timestamp <= filters.endDate!);
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => b.timestamp.localeCompare(a.timestamp));

    // Apply limit
    if (filters.limit) {
      filteredLogs = filteredLogs.slice(0, filters.limit);
    }

    return filteredLogs;
  }

  /**
   * Get audit statistics
   */
  getAuditStatistics(): {
    totalLogs: number;
    eventTypeCounts: Record<AuditEventType, number>;
    severityCounts: Record<AuditSeverity, number>;
    successRate: number;
    recentFailures: number;
  } {
    const eventTypeCounts = {} as Record<AuditEventType, number>;
    const severityCounts = {} as Record<AuditSeverity, number>;
    let successCount = 0;
    let recentFailures = 0;

    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);
    const oneDayAgoTimestamp = oneDayAgo.toISOString();

    for (const log of this.logs) {
      // Count event types
      eventTypeCounts[log.eventType] = (eventTypeCounts[log.eventType] || 0) + 1;

      // Count severities
      severityCounts[log.severity] = (severityCounts[log.severity] || 0) + 1;

      // Count successes
      if (log.success) {
        successCount++;
      }

      // Count recent failures
      if (!log.success && log.timestamp > oneDayAgoTimestamp) {
        recentFailures++;
      }
    }

    return {
      totalLogs: this.logs.length,
      eventTypeCounts,
      severityCounts,
      successRate: this.logs.length > 0 ? (successCount / this.logs.length) * 100 : 0,
      recentFailures
    };
  }
}

// Export singleton instance
export const auditLogger = new DatabaseAuditLogger();

// Export convenience functions
export const logDatabaseOperation = auditLogger.logDatabaseOperation.bind(auditLogger);
export const logAuthEvent = auditLogger.logAuthEvent.bind(auditLogger);
export const logSecurityEvent = auditLogger.logSecurityEvent.bind(auditLogger);
