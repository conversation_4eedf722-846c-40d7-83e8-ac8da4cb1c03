/**
 * Global Type Definitions
 * 
 * This file contains global type definitions for the Empire Pro
 * Cleaning Business application.
 */

// Environment Variables
declare namespace NodeJS {
  interface ProcessEnv {
    VITE_SUPABASE_URL: string;
    VITE_SUPABASE_ANON_KEY: string;
    VITE_SQUARE_APPLICATION_ID: string;
    VITE_SQUARE_ACCESS_TOKEN: string;
    VITE_SQUARE_LOCATION_ID: string;
    VITE_SQUARE_ENVIRONMENT: 'sandbox' | 'production';
    VITE_N8N_WEBHOOK_URL: string;
  }
}

// Import Meta Environment
interface ImportMetaEnv {
  readonly VITE_SUPABASE_URL: string;
  readonly VITE_SUPABASE_ANON_KEY: string;
  readonly VITE_SQUARE_APPLICATION_ID: string;
  readonly VITE_SQUARE_ACCESS_TOKEN: string;
  readonly VITE_SQUARE_LOCATION_ID: string;
  readonly VITE_SQUARE_ENVIRONMENT: 'sandbox' | 'production';
  readonly VITE_N8N_WEBHOOK_URL: string;
  readonly PROD: boolean;
  readonly DEV: boolean;
  readonly MODE: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// Common Data Types
export interface ContactInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

export interface PropertyDetails {
  type: 'house' | 'apartment' | 'condo' | 'office' | 'other';
  size: 'small' | 'medium' | 'large' | 'extra-large';
  bedrooms: string;
  bathrooms: string;
  address: string;
  city: string;
  zipCode: string;
  specialRequests?: string;
}

export interface ServiceDetails {
  frequency: 'one-time' | 'weekly' | 'bi-weekly' | 'monthly';
  cleaningType: 'standard' | 'deep' | 'move-in' | 'move-out';
  addOns: string[];
  totalPrice: number;
  specialInstructions?: string;
  isTest?: boolean;
}

export interface Schedule {
  preferredDate: string;
  preferredTime: string;
  timeFlexibility?: 'strict' | 'flexible' | 'very-flexible';
}

// Booking Types
export interface BookingData {
  id?: string;
  user_id: string | null;
  service_type: 'residential' | 'deep' | 'carpet' | 'commercial';
  status: 'pending' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled';
  contact: ContactInfo;
  property_details: PropertyDetails;
  service_details: ServiceDetails;
  schedule: Schedule;
  payment_status?: 'pending' | 'paid' | 'failed' | 'refunded';
  created_at?: string;
  updated_at?: string;
  metadata?: Record<string, any>;
}

// Payment Types
export interface PaymentRecord {
  id?: string;
  booking_id: string;
  payment_link_id: string;
  square_payment_id?: string;
  amount: number;
  currency: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  created_at?: string;
  updated_at?: string;
  metadata?: Record<string, any>;
}

export interface PaymentLink {
  id: string;
  url: string;
  orderId: string;
  amount: number;
  description: string;
  expiresAt?: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaymentResponse extends ApiResponse {
  paymentLink?: string;
  paymentId?: string;
  orderId?: string;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

// Form Types
export interface FormState {
  isValid: boolean;
  errors: ValidationError[];
  isSubmitting: boolean;
  isDirty: boolean;
}

// User Profile Types
export interface UserProfile {
  id: string;
  email: string;
  full_name: string;
  phone?: string;
  address?: string;
  created_at: string;
  updated_at: string;
  last_login?: string;
  preferences?: Record<string, any>;
}

// Configuration Types
export interface AppConfig {
  supabase: {
    url: string;
    anonKey: string;
    isConfigured: boolean;
  };
  square: {
    applicationId: string;
    accessToken: string;
    locationId: string;
    environment: 'sandbox' | 'production';
    isConfigured: boolean;
  };
  webhooks: {
    n8nUrl: string;
    isConfigured: boolean;
  };
  app: {
    environment: 'development' | 'production';
    isDevelopment: boolean;
    isProduction: boolean;
  };
}

// Utility Types
export type ServiceType = 'residential' | 'deep' | 'carpet' | 'commercial';
export type BookingStatus = 'pending' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled';
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded';

// Component Props Types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface FormComponentProps extends BaseComponentProps {
  onSubmit?: (data: any) => void | Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  initialData?: any;
}

// Hook Return Types
export interface UseFormReturn<T = any> {
  data: T;
  errors: ValidationError[];
  isValid: boolean;
  isSubmitting: boolean;
  isDirty: boolean;
  setValue: (field: string, value: any) => void;
  setError: (field: string, message: string) => void;
  clearErrors: () => void;
  submit: () => Promise<void>;
  reset: () => void;
}

// Database Table Types (matching Supabase schema)
export interface DbBookingForm {
  id: string;
  user_id: string | null;
  service_type: ServiceType;
  status: BookingStatus;
  contact: ContactInfo;
  property_details: PropertyDetails;
  service_details: ServiceDetails;
  schedule: Schedule;
  payment_status: PaymentStatus | null;
  created_at: string;
  updated_at: string;
  metadata: Record<string, any> | null;
}

export interface DbPaymentRecord {
  id: string;
  booking_id: string;
  payment_link_id: string;
  square_payment_id: string | null;
  amount: number;
  currency: string;
  status: string;
  created_at: string;
  updated_at: string;
  metadata: Record<string, any> | null;
}

export interface DbProfile {
  id: string;
  email: string;
  full_name: string;
  phone: string | null;
  address: string | null;
  created_at: string;
  updated_at: string;
  last_login: string | null;
  preferences: Record<string, any> | null;
}
