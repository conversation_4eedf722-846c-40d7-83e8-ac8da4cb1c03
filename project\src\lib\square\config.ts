import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../utils/errorHandler';
import { getEnvironmentConfig } from '../config/environment';

export interface SquareConfig {
  applicationId: string;
  locationId: string;
  accessToken: string;
  environment: 'sandbox' | 'production';
  isConfigured: boolean;
}

export function getSquareConfig(): SquareConfig {
  const envConfig = getEnvironmentConfig();

  if (!envConfig.square.isConfigured) {
    const error = ErrorHandler.createError(
      'SQUARE_NOT_CONFIGURED',
      'Square payment system is not properly configured. Please check your environment variables.',
      {
        hasApplicationId: <PERSON><PERSON>an(envConfig.square.applicationId),
        hasLocationId: <PERSON><PERSON><PERSON>(envConfig.square.locationId),
        hasAccessToken: Boole<PERSON>(envConfig.square.accessToken),
        environment: envConfig.square.environment
      }
    );
    ErrorHandler.logError(error);
  }

  return {
    applicationId: envConfig.square.applicationId,
    locationId: envConfig.square.locationId,
    accessToken: envConfig.square.accessToken,
    environment: envConfig.square.environment,
    isConfigured: envConfig.square.isConfigured
  };
}

export function validateSquareConfig(): { isValid: boolean; errors: string[] } {
  const config = getSquareConfig();
  const errors: string[] = [];

  if (!config.applicationId) {
    errors.push('VITE_SQUARE_APPLICATION_ID is missing');
  } else if (config.applicationId.includes('YourActual')) {
    errors.push('VITE_SQUARE_APPLICATION_ID contains placeholder text');
  }

  if (!config.locationId) {
    errors.push('VITE_SQUARE_LOCATION_ID is missing');
  } else if (config.locationId.includes('YourActual')) {
    errors.push('VITE_SQUARE_LOCATION_ID contains placeholder text');
  }

  if (!config.accessToken) {
    errors.push('VITE_SQUARE_ACCESS_TOKEN is missing');
  } else if (config.accessToken.includes('YourActual')) {
    errors.push('VITE_SQUARE_ACCESS_TOKEN contains placeholder text');
  } else if (config.accessToken.length < 20) {
    errors.push('VITE_SQUARE_ACCESS_TOKEN appears to be invalid (too short)');
  }

  if (!['sandbox', 'production'].includes(config.environment)) {
    errors.push('VITE_SQUARE_ENVIRONMENT must be either "sandbox" or "production"');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function getSquarePaymentFormConfig() {
  const config = getSquareConfig();
  
  if (!config.isConfigured) {
    throw new Error('Square is not configured. Please set up your Square credentials.');
  }

  return {
    applicationId: config.applicationId,
    locationId: config.locationId,
    environment: config.environment
  };
} 