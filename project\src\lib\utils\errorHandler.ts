export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

export class ErrorHandler {
  private static errorLog: AppError[] = [];
  private static readonly MAX_LOG_SIZE = 1000;
  private static readonly SENSITIVE_PATTERNS = [
    /password/i,
    /token/i,
    /secret/i,
    /key/i,
    /auth/i,
    /credential/i,
    /session/i,
    /api[_-]?key/i,
    /access[_-]?token/i,
    /refresh[_-]?token/i
  ];

  static createError(code: string, message: string, details?: any): AppError {
    return {
      code,
      message: this.sanitizeString(message),
      details: this.sanitizeDetails(details),
      timestamp: new Date()
    };
  }

  /**
   * Sanitize error details to remove sensitive information
   */
  private static sanitizeDetails(details: any): any {
    if (!details) return details;

    if (typeof details === 'string') {
      return this.sanitizeString(details);
    }

    if (typeof details === 'object') {
      const sanitized: any = {};

      for (const [key, value] of Object.entries(details)) {
        // Check if key contains sensitive information
        const isSensitiveKey = this.SENSITIVE_PATTERNS.some(pattern => pattern.test(key));

        if (isSensitiveKey) {
          sanitized[key] = '[REDACTED]';
        } else if (typeof value === 'string') {
          sanitized[key] = this.sanitizeString(value);
        } else if (typeof value === 'object' && value !== null) {
          sanitized[key] = this.sanitizeDetails(value);
        } else {
          sanitized[key] = value;
        }
      }

      return sanitized;
    }

    return details;
  }

  /**
   * Sanitize string values to remove sensitive information
   */
  private static sanitizeString(str: string): string {
    let sanitized = str;

    // Redact potential tokens, keys, and passwords
    this.SENSITIVE_PATTERNS.forEach(pattern => {
      sanitized = sanitized.replace(new RegExp(`(${pattern.source})\\s*[:=]\\s*[^\\s,}]+`, 'gi'), '$1: [REDACTED]');
    });

    // Redact JWT tokens
    sanitized = sanitized.replace(/eyJ[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*/g, '[JWT_TOKEN_REDACTED]');

    // Redact email addresses in error messages (partial)
    sanitized = sanitized.replace(/([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g, '$1@[DOMAIN_REDACTED]');

    return sanitized;
  }

  /**
   * Log error securely without exposing sensitive information
   */
  static logError(error: AppError): void {
    // Add to internal log
    this.errorLog.push(error);

    // Maintain log size
    if (this.errorLog.length > this.MAX_LOG_SIZE) {
      this.errorLog = this.errorLog.slice(-this.MAX_LOG_SIZE);
    }

    // Log to console in development (sanitized)
    if (!import.meta.env.PROD) {
      console.error('🚨 Error:', {
        code: error.code,
        message: error.message,
        timestamp: error.timestamp,
        details: this.sanitizeDetails(error.details)
      });
    }

    // In production, send to secure logging service
    if (import.meta.env.PROD) {
      this.sendToSecureLogging(error);
    }
  }

  /**
   * Log success message
   */
  static logSuccess(message: string, details?: any): void {
    const sanitizedDetails = this.sanitizeDetails(details);

    if (!import.meta.env.PROD) {
      console.log('✅ Success:', message, sanitizedDetails);
    }
  }

  /**
   * Log info message
   */
  static logInfo(message: string, details?: any): void {
    const sanitizedDetails = this.sanitizeDetails(details);

    if (!import.meta.env.PROD) {
      console.info('ℹ️ Info:', message, sanitizedDetails);
    }
  }

  /**
   * Log warning message
   */
  static logWarning(message: string, details?: any): void {
    const sanitizedDetails = this.sanitizeDetails(details);

    if (!import.meta.env.PROD) {
      console.warn('⚠️ Warning:', message, sanitizedDetails);
    }
  }

  /**
   * Send error to secure logging service
   */
  private static sendToSecureLogging(error: AppError): void {
    // In production, implement secure logging to external service
    // For now, store sanitized version in localStorage
    try {
      const sanitizedError = {
        code: error.code,
        message: error.message,
        timestamp: error.timestamp.toISOString(),
        details: this.sanitizeDetails(error.details)
      };

      const existingLogs = JSON.parse(localStorage.getItem('errorLogs') || '[]');
      existingLogs.push(sanitizedError);

      // Keep only recent logs
      const recentLogs = existingLogs.slice(-100);
      localStorage.setItem('errorLogs', JSON.stringify(recentLogs));
    } catch (logError) {
      console.error('Failed to store error log:', logError);
    }
  }

  /**
   * Get sanitized error logs for debugging
   */
  static getErrorLogs(limit: number = 50): Array<{
    code: string;
    message: string;
    timestamp: string;
    details?: any;
  }> {
    return this.errorLog
      .slice(-limit)
      .map(error => ({
        code: error.code,
        message: error.message,
        timestamp: error.timestamp.toISOString(),
        details: this.sanitizeDetails(error.details)
      }));
  }

  /**
   * Clear error logs
   */
  static clearErrorLogs(): void {
    this.errorLog = [];
    localStorage.removeItem('errorLogs');
  }

  static handleAuthError(error: any): AppError {
    let message = 'Authentication failed';
    let code = 'AUTH_ERROR';
    
    if (error.message) {
      if (typeof error.message === 'string') {
        if (error.message.includes('User already registered') || error.message.includes('user_already_exists')) {
          message = 'An account with this email already exists. Please sign in instead.';
          code = 'USER_EXISTS';
        } else if (error.message.includes('Invalid login credentials')) {
          message = 'Invalid email or password. Please try again.';
          code = 'INVALID_CREDENTIALS';
        } else if (error.message.includes('rate limit')) {
          message = 'Too many attempts. Please try again later.';
          code = 'RATE_LIMITED';
        } else if (error.message.includes('Email not confirmed')) {
          message = 'Please check your email and click the confirmation link.';
          code = 'EMAIL_NOT_CONFIRMED';
        } else {
          // For unknown errors, provide a generic message but log the actual error securely
          message = 'Authentication failed. Please try again.';
          const authError = this.createError('UNKNOWN_AUTH_ERROR', 'Unknown authentication error occurred', error);
          this.logError(authError);
        }
      }
    }

    return this.createError(code, message, { originalError: error.message });
  }

  static handleApiError(error: any): AppError {
    let message = 'An error occurred while processing your request';
    let code = 'API_ERROR';
    
    if (error.response) {
      // HTTP error response
      const status = error.response.status;
      switch (status) {
        case 400:
          message = 'Invalid request. Please check your information.';
          code = 'BAD_REQUEST';
          break;
        case 401:
          message = 'Please log in to continue.';
          code = 'UNAUTHORIZED';
          break;
        case 403:
          message = 'You don\'t have permission to perform this action.';
          code = 'FORBIDDEN';
          break;
        case 404:
          message = 'The requested resource was not found.';
          code = 'NOT_FOUND';
          break;
        case 429:
          message = 'Too many requests. Please try again later.';
          code = 'RATE_LIMITED';
          break;
        case 500:
          message = 'Server error. Please try again later.';
          code = 'SERVER_ERROR';
          break;
        default:
          message = error.response.data?.message || message;
      }
    } else if (error.request) {
      message = 'Network error. Please check your internet connection.';
      code = 'NETWORK_ERROR';
    }
    
    return this.createError(code, message, error);
  }

  static handleSupabaseError(error: any): AppError {
    let message = 'Database error occurred';
    let code = 'DATABASE_ERROR';
    
    if (error.code === 'PGRST116') {
      message = 'No data found for your request.';
      code = 'NO_DATA';
    } else if (error.code === '23505') {
      message = 'This record already exists.';
      code = 'DUPLICATE_ENTRY';
    } else if (error.code === '23503') {
      message = 'Referenced record not found.';
      code = 'FOREIGN_KEY_ERROR';
    } else if (error.message) {
      message = error.message;
    }
    
    return this.createError(code, message, error);
  }

  static handleFormValidationError(field: string, value: any, rule: string): AppError {
    let message = 'Invalid input';
    const code = 'VALIDATION_ERROR';
    
    switch (rule) {
      case 'required':
        message = `${field} is required`;
        break;
      case 'email':
        message = 'Please enter a valid email address';
        break;
      case 'phone':
        message = 'Please enter a valid phone number';
        break;
      case 'minLength':
        message = `${field} must be at least ${value} characters`;
        break;
      case 'maxLength':
        message = `${field} must be no more than ${value} characters`;
        break;
      case 'pattern':
        message = `${field} format is invalid`;
        break;
      default:
        message = `${field} is invalid`;
    }
    
    return this.createError(code, message, { field, value, rule });
  }


} 