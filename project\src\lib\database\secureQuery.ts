/**
 * Secure Database Query Builder
 * 
 * Provides a secure interface for database operations with built-in
 * protection against SQL injection and other database security threats.
 */

import { supabase } from '../supabase/client';
import { InputSanitizer } from '../utils/validation';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../utils/errorHandler';

// Query operation types
export type QueryOperation = 'select' | 'insert' | 'update' | 'delete';

// Security levels for different operations
export enum SecurityLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Query context for security validation
export interface QueryContext {
  operation: QueryOperation;
  table: string;
  userId?: string;
  userRole?: string;
  securityLevel: SecurityLevel;
}

// Allowed tables and their security configurations
const TABLE_SECURITY_CONFIG: Record<string, {
  allowedOperations: QueryOperation[];
  securityLevel: SecurityLevel;
  requiresAuth: boolean;
  ownershipField?: string;
}> = {
  'booking_forms': {
    allowedOperations: ['select', 'insert', 'update', 'delete'],
    securityLevel: SecurityLevel.HIGH,
    requiresAuth: true,
    ownershipField: 'user_id'
  },
  'payment_records': {
    allowedOperations: ['select', 'insert', 'update'],
    securityLevel: SecurityLevel.CRITICAL,
    requiresAuth: true,
    ownershipField: 'user_id'
  },
  'profiles': {
    allowedOperations: ['select', 'insert', 'update'],
    securityLevel: SecurityLevel.HIGH,
    requiresAuth: true,
    ownershipField: 'id'
  }
};

// Dangerous patterns that should be blocked
const DANGEROUS_PATTERNS = [
  /;\s*(drop|delete|truncate|alter)\s+/i,
  /union\s+select/i,
  /exec\s*\(/i,
  /script\s*>/i,
  /<\s*script/i,
  /javascript:/i,
  /vbscript:/i,
  /onload\s*=/i,
  /onerror\s*=/i
];

class SecureQueryBuilder {
  /**
   * Validate query security
   */
  private validateQuerySecurity(context: QueryContext): void {
    const tableConfig = TABLE_SECURITY_CONFIG[context.table];
    
    if (!tableConfig) {
      throw new Error(`Table '${context.table}' is not allowed for queries`);
    }

    if (!tableConfig.allowedOperations.includes(context.operation)) {
      throw new Error(`Operation '${context.operation}' is not allowed on table '${context.table}'`);
    }

    if (tableConfig.requiresAuth && !context.userId) {
      throw new Error(`Authentication required for operations on table '${context.table}'`);
    }
  }

  /**
   * Sanitize query parameters
   */
  private sanitizeParameters(params: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};

    for (const [key, value] of Object.entries(params)) {
      if (typeof value === 'string') {
        // Check for dangerous patterns
        for (const pattern of DANGEROUS_PATTERNS) {
          if (pattern.test(value)) {
            throw new Error(`Potentially dangerous content detected in parameter '${key}'`);
          }
        }

        // Sanitize the string
        sanitized[key] = InputSanitizer.sanitizeSql(value);
      } else if (typeof value === 'number') {
        // Validate numbers
        if (!isFinite(value) || isNaN(value)) {
          throw new Error(`Invalid number value for parameter '${key}'`);
        }
        sanitized[key] = value;
      } else if (typeof value === 'boolean') {
        sanitized[key] = value;
      } else if (value === null || value === undefined) {
        sanitized[key] = value;
      } else if (Array.isArray(value)) {
        // Recursively sanitize array elements
        sanitized[key] = value.map(item => 
          typeof item === 'string' ? InputSanitizer.sanitizeSql(item) : item
        );
      } else if (typeof value === 'object') {
        // Recursively sanitize object properties
        sanitized[key] = this.sanitizeParameters(value);
      } else {
        throw new Error(`Unsupported parameter type for '${key}': ${typeof value}`);
      }
    }

    return sanitized;
  }

  /**
   * Add ownership filter to query
   */
  private addOwnershipFilter(query: any, context: QueryContext): any {
    const tableConfig = TABLE_SECURITY_CONFIG[context.table];
    
    if (tableConfig.ownershipField && context.userId) {
      // Add ownership filter unless user has admin privileges
      if (context.userRole !== 'admin' && context.userRole !== 'super_admin') {
        query = query.eq(tableConfig.ownershipField, context.userId);
      }
    }

    return query;
  }

  /**
   * Secure SELECT operation
   */
  async secureSelect(
    table: string,
    columns: string = '*',
    filters: Record<string, any> = {},
    options: {
      userId?: string;
      userRole?: string;
      limit?: number;
      offset?: number;
      orderBy?: string;
      ascending?: boolean;
    } = {}
  ) {
    if (!supabase) {
      throw new Error('Database client not available');
    }

    const context: QueryContext = {
      operation: 'select',
      table,
      userId: options.userId,
      userRole: options.userRole,
      securityLevel: TABLE_SECURITY_CONFIG[table]?.securityLevel || SecurityLevel.MEDIUM
    };

    try {
      this.validateQuerySecurity(context);
      const sanitizedFilters = this.sanitizeParameters(filters);

      let query = supabase.from(table).select(columns);

      // Apply filters
      for (const [key, value] of Object.entries(sanitizedFilters)) {
        if (value !== undefined && value !== null) {
          query = query.eq(key, value);
        }
      }

      // Add ownership filter
      query = this.addOwnershipFilter(query, context);

      // Apply pagination
      if (options.limit) {
        query = query.limit(Math.min(options.limit, 1000)); // Max 1000 records
      }

      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 100) - 1);
      }

      // Apply ordering
      if (options.orderBy) {
        query = query.order(options.orderBy, { ascending: options.ascending !== false });
      }

      const { data, error } = await query;

      if (error) {
        ErrorHandler.logError(ErrorHandler.createError('DATABASE_ERROR', 'Query failed', error));
        throw error;
      }

      return data;
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.createError('SECURE_QUERY_ERROR', 'Secure select failed', error));
      throw error;
    }
  }

  /**
   * Secure INSERT operation
   */
  async secureInsert(
    table: string,
    data: Record<string, any> | Record<string, any>[],
    options: {
      userId?: string;
      userRole?: string;
      returnData?: boolean;
    } = {}
  ) {
    if (!supabase) {
      throw new Error('Database client not available');
    }

    const context: QueryContext = {
      operation: 'insert',
      table,
      userId: options.userId,
      userRole: options.userRole,
      securityLevel: TABLE_SECURITY_CONFIG[table]?.securityLevel || SecurityLevel.MEDIUM
    };

    try {
      this.validateQuerySecurity(context);

      // Sanitize data
      const sanitizedData = Array.isArray(data) 
        ? data.map(item => this.sanitizeParameters(item))
        : this.sanitizeParameters(data);

      // Add ownership field if required
      const tableConfig = TABLE_SECURITY_CONFIG[table];
      if (tableConfig.ownershipField && context.userId) {
        if (Array.isArray(sanitizedData)) {
          sanitizedData.forEach(item => {
            if (!item[tableConfig.ownershipField]) {
              item[tableConfig.ownershipField] = context.userId;
            }
          });
        } else {
          if (!sanitizedData[tableConfig.ownershipField]) {
            sanitizedData[tableConfig.ownershipField] = context.userId;
          }
        }
      }

      let query = supabase.from(table).insert(sanitizedData);

      if (options.returnData) {
        query = query.select();
      }

      const { data: result, error } = await query;

      if (error) {
        ErrorHandler.logError(ErrorHandler.createError('DATABASE_ERROR', 'Insert failed', error));
        throw error;
      }

      return result;
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.createError('SECURE_QUERY_ERROR', 'Secure insert failed', error));
      throw error;
    }
  }

  /**
   * Secure UPDATE operation
   */
  async secureUpdate(
    table: string,
    data: Record<string, any>,
    filters: Record<string, any>,
    options: {
      userId?: string;
      userRole?: string;
      returnData?: boolean;
    } = {}
  ) {
    if (!supabase) {
      throw new Error('Database client not available');
    }

    const context: QueryContext = {
      operation: 'update',
      table,
      userId: options.userId,
      userRole: options.userRole,
      securityLevel: TABLE_SECURITY_CONFIG[table]?.securityLevel || SecurityLevel.MEDIUM
    };

    try {
      this.validateQuerySecurity(context);

      const sanitizedData = this.sanitizeParameters(data);
      const sanitizedFilters = this.sanitizeParameters(filters);

      // Add updated_at timestamp
      sanitizedData.updated_at = new Date().toISOString();

      let query = supabase.from(table).update(sanitizedData);

      // Apply filters
      for (const [key, value] of Object.entries(sanitizedFilters)) {
        if (value !== undefined && value !== null) {
          query = query.eq(key, value);
        }
      }

      // Add ownership filter
      query = this.addOwnershipFilter(query, context);

      if (options.returnData) {
        query = query.select();
      }

      const { data: result, error } = await query;

      if (error) {
        ErrorHandler.logError(ErrorHandler.createError('DATABASE_ERROR', 'Update failed', error));
        throw error;
      }

      return result;
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.createError('SECURE_QUERY_ERROR', 'Secure update failed', error));
      throw error;
    }
  }

  /**
   * Secure DELETE operation
   */
  async secureDelete(
    table: string,
    filters: Record<string, any>,
    options: {
      userId?: string;
      userRole?: string;
      returnData?: boolean;
    } = {}
  ) {
    if (!supabase) {
      throw new Error('Database client not available');
    }

    const context: QueryContext = {
      operation: 'delete',
      table,
      userId: options.userId,
      userRole: options.userRole,
      securityLevel: TABLE_SECURITY_CONFIG[table]?.securityLevel || SecurityLevel.CRITICAL
    };

    try {
      this.validateQuerySecurity(context);

      const sanitizedFilters = this.sanitizeParameters(filters);

      let query = supabase.from(table).delete();

      // Apply filters
      for (const [key, value] of Object.entries(sanitizedFilters)) {
        if (value !== undefined && value !== null) {
          query = query.eq(key, value);
        }
      }

      // Add ownership filter
      query = this.addOwnershipFilter(query, context);

      if (options.returnData) {
        query = query.select();
      }

      const { data: result, error } = await query;

      if (error) {
        ErrorHandler.logError(ErrorHandler.createError('DATABASE_ERROR', 'Delete failed', error));
        throw error;
      }

      return result;
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.createError('SECURE_QUERY_ERROR', 'Secure delete failed', error));
      throw error;
    }
  }
}

// Export singleton instance
export const secureQuery = new SecureQueryBuilder();

// Export convenience functions
export const secureSelect = secureQuery.secureSelect.bind(secureQuery);
export const secureInsert = secureQuery.secureInsert.bind(secureQuery);
export const secureUpdate = secureQuery.secureUpdate.bind(secureQuery);
export const secureDelete = secureQuery.secureDelete.bind(secureQuery);
