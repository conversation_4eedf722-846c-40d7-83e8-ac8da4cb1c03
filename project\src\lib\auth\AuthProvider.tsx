import React, { createContext, useContext, useEffect, useState } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase, isSupabaseConfigured } from '../supabase/client';
import { <PERSON>rrorHand<PERSON> } from '../utils/errorHandler';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, fullName: string) => Promise<void>;
  signOut: () => Promise<void>;
  createGuestAccount: (email: string, fullName: string) => Promise<User>;
  saveFormData: (data: any) => void;
  pendingFormData: any;
  clearPendingData: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Generate cryptographically secure password
function generateSecurePassword(length: number = 32): string {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  const array = new Uint8Array(length);

  // Use crypto.getRandomValues for cryptographically secure randomness
  if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
    window.crypto.getRandomValues(array);
  } else {
    // Fallback for environments without crypto.getRandomValues
    for (let i = 0; i < length; i++) {
      array[i] = Math.floor(Math.random() * 256);
    }
  }

  return Array.from(array, byte => charset[byte % charset.length]).join('');
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [pendingFormData, setPendingFormData] = useState<any>(null);

  useEffect(() => {
    if (!isSupabaseConfigured) {
      setLoading(false);
      return;
    }

    // Set up auth state listener
    const setupAuthListener = async () => {
      try {
        if (!supabase) {
          console.error('Supabase client not available');
          setLoading(false);
          return;
        }

        // Get initial session
        const { data: { session } } = await supabase.auth.getSession();
        setUser(session?.user ?? null);

        // Set up auth state change subscription
        const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
          setUser(session?.user ?? null);
        });
        
        setLoading(false);
        
        // Clean up subscription on unmount
        return () => subscription.unsubscribe();
      } catch (error) {
        console.error('Error setting up auth listener:', error);
        setLoading(false);
      }
    };

    setupAuthListener();
  }, []);

  const handleAuthError = (error: any) => {
    const appError = ErrorHandler.handleAuthError(error);
    ErrorHandler.logError(appError);
    throw new Error(appError.message);
  };

  const createProfile = async (userId: string, email: string, fullName: string) => {
    if (!isSupabaseConfigured) {
      throw new Error('Supabase is not configured. Please check your environment variables.');
    }

    try {
      // First check if profile exists
      const { data: existingProfile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .maybeSingle();

      if (!existingProfile) {
        // Create new profile if it doesn't exist
        const { error } = await supabase
          .from('profiles')
          .insert([{
            id: userId,
            email: email,
            full_name: fullName,
            updated_at: new Date().toISOString()
          }]);

        if (error) throw error;
      }
    } catch (error) {
      console.error('Error creating profile:', error);
      throw error;
    }
  };

  const signIn = async (email: string, password: string) => {
    if (!isSupabaseConfigured) {
      throw new Error('Supabase is not configured. Please check your environment variables.');
    }

    try {
      const { data, error } = await supabase.auth.signInWithPassword({ email, password });
      
      if (error) throw error;
      
      // Update last login time in profile
      if (data.user) {
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ last_login: new Date().toISOString() })
          .eq('id', data.user.id);
          
        if (updateError) {
          console.error('Error updating last login:', updateError);
          // Don't throw, just log the error
        }
      }
    } catch (error) {
      handleAuthError(error);
    }
  };

  const signUp = async (email: string, password: string, fullName: string) => {
    if (!isSupabaseConfigured) {
      throw new Error('Supabase is not configured. Please check your environment variables.');
    }

    try {
      const { data: { user }, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: { 
            full_name: fullName,
            email: email
          }
        }
      });

      if (error) throw error;
      if (!user) throw new Error('Failed to create account');

      // Create profile
      await createProfile(user.id, email, fullName);

    } catch (error) {
      handleAuthError(error);
    }
  };

  const signOut = async () => {
    if (!isSupabaseConfigured) {
      throw new Error('Supabase is not configured. Please check your environment variables.');
    }

    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      setPendingFormData(null);
    } catch (error) {
      handleAuthError(error);
    }
  };

  const createGuestAccount = async (email: string, fullName: string) => {
    if (!isSupabaseConfigured) {
      throw new Error('Supabase is not configured. Please check your environment variables.');
    }

    try {
      // Generate a cryptographically secure random password
      const password = generateSecurePassword();
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            email: email,
            is_guest: true
          }
        }
      });

      if (error) throw error;
      if (!data.user) throw new Error('Failed to create guest account');

      // Create profile
      await createProfile(data.user.id, email, fullName);

      return data.user;
    } catch (error) {
      handleAuthError(error);
      throw error;
    }
  };

  const saveFormData = (data: any) => {
    setPendingFormData(data);
  };

  const clearPendingData = () => {
    setPendingFormData(null);
  };

  return (
    <AuthContext.Provider value={{ 
      user, 
      loading, 
      signIn, 
      signUp, 
      signOut,
      createGuestAccount,
      saveFormData,
      pendingFormData,
      clearPendingData
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}