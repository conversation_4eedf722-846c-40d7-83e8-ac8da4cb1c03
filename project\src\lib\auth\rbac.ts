/**
 * Role-Based Access Control (RBAC) System
 * 
 * Implements a flexible RBAC system for managing user permissions
 * and access control throughout the application.
 */

// Define available permissions
export enum Permission {
  // Booking permissions
  CREATE_BOOKING = 'create_booking',
  VIEW_OWN_BOOKINGS = 'view_own_bookings',
  VIEW_ALL_BOOKINGS = 'view_all_bookings',
  EDIT_OWN_BOOKINGS = 'edit_own_bookings',
  EDIT_ALL_BOOKINGS = 'edit_all_bookings',
  DELETE_OWN_BOOKINGS = 'delete_own_bookings',
  DELETE_ALL_BOOKINGS = 'delete_all_bookings',
  
  // Payment permissions
  PROCESS_PAYMENTS = 'process_payments',
  VIEW_PAYMENT_HISTORY = 'view_payment_history',
  REFUND_PAYMENTS = 'refund_payments',
  
  // User management permissions
  VIEW_PROFILE = 'view_profile',
  EDIT_PROFILE = 'edit_profile',
  VIEW_ALL_USERS = 'view_all_users',
  EDIT_ALL_USERS = 'edit_all_users',
  DELETE_USERS = 'delete_users',
  
  // Administrative permissions
  ACCESS_ADMIN_PANEL = 'access_admin_panel',
  MANAGE_SYSTEM_SETTINGS = 'manage_system_settings',
  VIEW_ANALYTICS = 'view_analytics',
  EXPORT_DATA = 'export_data',
  
  // Debug permissions
  ACCESS_DEBUG_TOOLS = 'access_debug_tools',
  VIEW_LOGS = 'view_logs'
}

// Define roles
export enum Role {
  GUEST = 'guest',
  CUSTOMER = 'customer',
  STAFF = 'staff',
  MANAGER = 'manager',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}

// Role hierarchy (higher number = more permissions)
const ROLE_HIERARCHY: Record<Role, number> = {
  [Role.GUEST]: 0,
  [Role.CUSTOMER]: 1,
  [Role.STAFF]: 2,
  [Role.MANAGER]: 3,
  [Role.ADMIN]: 4,
  [Role.SUPER_ADMIN]: 5
};

// Define permissions for each role
const ROLE_PERMISSIONS: Record<Role, Permission[]> = {
  [Role.GUEST]: [
    Permission.CREATE_BOOKING,
    Permission.PROCESS_PAYMENTS
  ],
  
  [Role.CUSTOMER]: [
    Permission.CREATE_BOOKING,
    Permission.VIEW_OWN_BOOKINGS,
    Permission.EDIT_OWN_BOOKINGS,
    Permission.DELETE_OWN_BOOKINGS,
    Permission.PROCESS_PAYMENTS,
    Permission.VIEW_PAYMENT_HISTORY,
    Permission.VIEW_PROFILE,
    Permission.EDIT_PROFILE
  ],
  
  [Role.STAFF]: [
    Permission.CREATE_BOOKING,
    Permission.VIEW_OWN_BOOKINGS,
    Permission.VIEW_ALL_BOOKINGS,
    Permission.EDIT_OWN_BOOKINGS,
    Permission.EDIT_ALL_BOOKINGS,
    Permission.PROCESS_PAYMENTS,
    Permission.VIEW_PAYMENT_HISTORY,
    Permission.VIEW_PROFILE,
    Permission.EDIT_PROFILE,
    Permission.VIEW_ALL_USERS
  ],
  
  [Role.MANAGER]: [
    Permission.CREATE_BOOKING,
    Permission.VIEW_OWN_BOOKINGS,
    Permission.VIEW_ALL_BOOKINGS,
    Permission.EDIT_OWN_BOOKINGS,
    Permission.EDIT_ALL_BOOKINGS,
    Permission.DELETE_ALL_BOOKINGS,
    Permission.PROCESS_PAYMENTS,
    Permission.VIEW_PAYMENT_HISTORY,
    Permission.REFUND_PAYMENTS,
    Permission.VIEW_PROFILE,
    Permission.EDIT_PROFILE,
    Permission.VIEW_ALL_USERS,
    Permission.EDIT_ALL_USERS,
    Permission.VIEW_ANALYTICS,
    Permission.EXPORT_DATA
  ],
  
  [Role.ADMIN]: [
    Permission.CREATE_BOOKING,
    Permission.VIEW_OWN_BOOKINGS,
    Permission.VIEW_ALL_BOOKINGS,
    Permission.EDIT_OWN_BOOKINGS,
    Permission.EDIT_ALL_BOOKINGS,
    Permission.DELETE_ALL_BOOKINGS,
    Permission.PROCESS_PAYMENTS,
    Permission.VIEW_PAYMENT_HISTORY,
    Permission.REFUND_PAYMENTS,
    Permission.VIEW_PROFILE,
    Permission.EDIT_PROFILE,
    Permission.VIEW_ALL_USERS,
    Permission.EDIT_ALL_USERS,
    Permission.DELETE_USERS,
    Permission.ACCESS_ADMIN_PANEL,
    Permission.MANAGE_SYSTEM_SETTINGS,
    Permission.VIEW_ANALYTICS,
    Permission.EXPORT_DATA,
    Permission.ACCESS_DEBUG_TOOLS,
    Permission.VIEW_LOGS
  ],
  
  [Role.SUPER_ADMIN]: Object.values(Permission)
};

// User interface
export interface User {
  id: string;
  email: string;
  role: Role;
  permissions?: Permission[];
  isActive: boolean;
}

class RBACManager {
  /**
   * Check if a user has a specific permission
   */
  hasPermission(user: User | null, permission: Permission): boolean {
    if (!user || !user.isActive) return false;

    // Check custom permissions first
    if (user.permissions && user.permissions.includes(permission)) {
      return true;
    }

    // Check role-based permissions
    const rolePermissions = ROLE_PERMISSIONS[user.role] || [];
    return rolePermissions.includes(permission);
  }

  /**
   * Check if a user has any of the specified permissions
   */
  hasAnyPermission(user: User | null, permissions: Permission[]): boolean {
    return permissions.some(permission => this.hasPermission(user, permission));
  }

  /**
   * Check if a user has all of the specified permissions
   */
  hasAllPermissions(user: User | null, permissions: Permission[]): boolean {
    return permissions.every(permission => this.hasPermission(user, permission));
  }

  /**
   * Check if a user has a specific role or higher
   */
  hasRoleOrHigher(user: User | null, role: Role): boolean {
    if (!user || !user.isActive) return false;
    
    const userRoleLevel = ROLE_HIERARCHY[user.role] || 0;
    const requiredRoleLevel = ROLE_HIERARCHY[role] || 0;
    
    return userRoleLevel >= requiredRoleLevel;
  }

  /**
   * Get all permissions for a user
   */
  getUserPermissions(user: User | null): Permission[] {
    if (!user || !user.isActive) return [];

    const rolePermissions = ROLE_PERMISSIONS[user.role] || [];
    const customPermissions = user.permissions || [];

    // Combine and deduplicate permissions
    return [...new Set([...rolePermissions, ...customPermissions])];
  }

  /**
   * Check if a user can access a resource owned by another user
   */
  canAccessResource(user: User | null, resourceOwnerId: string): boolean {
    if (!user || !user.isActive) return false;

    // Users can always access their own resources
    if (user.id === resourceOwnerId) return true;

    // Check if user has permissions to access all resources
    return this.hasAnyPermission(user, [
      Permission.VIEW_ALL_BOOKINGS,
      Permission.EDIT_ALL_BOOKINGS,
      Permission.DELETE_ALL_BOOKINGS,
      Permission.VIEW_ALL_USERS,
      Permission.EDIT_ALL_USERS
    ]);
  }

  /**
   * Filter resources based on user permissions
   */
  filterAccessibleResources<T extends { user_id?: string; owner_id?: string }>(
    user: User | null,
    resources: T[]
  ): T[] {
    if (!user || !user.isActive) return [];

    // If user can access all resources, return everything
    if (this.canAccessResource(user, '')) return resources;

    // Filter to only resources owned by the user
    return resources.filter(resource => {
      const ownerId = resource.user_id || resource.owner_id;
      return ownerId === user.id;
    });
  }

  /**
   * Create a permission guard function
   */
  createPermissionGuard(permission: Permission) {
    return (user: User | null): boolean => {
      return this.hasPermission(user, permission);
    };
  }

  /**
   * Create a role guard function
   */
  createRoleGuard(role: Role) {
    return (user: User | null): boolean => {
      return this.hasRoleOrHigher(user, role);
    };
  }

  /**
   * Get role display name
   */
  getRoleDisplayName(role: Role): string {
    const displayNames: Record<Role, string> = {
      [Role.GUEST]: 'Guest',
      [Role.CUSTOMER]: 'Customer',
      [Role.STAFF]: 'Staff Member',
      [Role.MANAGER]: 'Manager',
      [Role.ADMIN]: 'Administrator',
      [Role.SUPER_ADMIN]: 'Super Administrator'
    };

    return displayNames[role] || role;
  }

  /**
   * Get permission display name
   */
  getPermissionDisplayName(permission: Permission): string {
    return permission
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }
}

// Export singleton instance
export const rbac = new RBACManager();

// Export convenience functions
export const hasPermission = (user: User | null, permission: Permission) => 
  rbac.hasPermission(user, permission);

export const hasRole = (user: User | null, role: Role) => 
  rbac.hasRoleOrHigher(user, role);

export const canAccessResource = (user: User | null, resourceOwnerId: string) => 
  rbac.canAccessResource(user, resourceOwnerId);

// Permission guards for common use cases
export const requireAuth = (user: User | null): boolean => user !== null && user.isActive;
export const requireCustomer = rbac.createRoleGuard(Role.CUSTOMER);
export const requireStaff = rbac.createRoleGuard(Role.STAFF);
export const requireManager = rbac.createRoleGuard(Role.MANAGER);
export const requireAdmin = rbac.createRoleGuard(Role.ADMIN);

export const canCreateBooking = rbac.createPermissionGuard(Permission.CREATE_BOOKING);
export const canViewAllBookings = rbac.createPermissionGuard(Permission.VIEW_ALL_BOOKINGS);
export const canProcessPayments = rbac.createPermissionGuard(Permission.PROCESS_PAYMENTS);
export const canAccessAdmin = rbac.createPermissionGuard(Permission.ACCESS_ADMIN_PANEL);
export const canAccessDebug = rbac.createPermissionGuard(Permission.ACCESS_DEBUG_TOOLS);
