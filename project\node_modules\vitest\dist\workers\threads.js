import { r as runBaseTests } from '../chunks/base.BZZh4cSm.js';
import { a as createThreadsRpcOptions } from '../chunks/utils.Cn0zI1t3.js';
import 'vite-node/client';
import '../chunks/execute.2pr0rHgK.js';
import 'node:fs';
import 'node:url';
import 'node:vm';
import '@vitest/utils/error';
import 'pathe';
import 'vite-node/utils';
import '../path.js';
import 'node:path';
import '@vitest/mocker';
import 'node:module';
import '@vitest/utils';
import '../chunks/utils.C8RiOc4B.js';

class ThreadsBaseWorker {
  getRpcOptions(ctx) {
    return createThreadsRpcOptions(ctx);
  }
  runTests(state) {
    return runBaseTests("run", state);
  }
  collectTests(state) {
    return runBaseTests("collect", state);
  }
}
var threads = new ThreadsBaseWorker();

export { threads as default };
