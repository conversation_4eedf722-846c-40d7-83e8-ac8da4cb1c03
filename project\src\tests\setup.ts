/**
 * Test Setup Configuration
 * 
 * Global setup for all tests including mocks, security configurations,
 * and test utilities.
 */

import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { cleanup } from '@testing-library/react';

// Mock environment variables for consistent testing
const mockEnv = {
  VITE_SUPABASE_URL: 'https://test-project.supabase.co',
  VITE_SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.key',
  VITE_SQUARE_APPLICATION_ID: 'sandbox-sq0idb-test-app-id',
  VITE_SQUARE_ACCESS_TOKEN: 'EAAAl-test-access-token',
  VITE_SQUARE_LOCATION_ID: 'test-location-id',
  VITE_SQUARE_ENVIRONMENT: 'sandbox',
  VITE_N8N_WEBHOOK_URL: 'https://test.app.n8n.cloud/webhook/test-id',
  PROD: false,
  DEV: true,
  MODE: 'test'
};

// Global test setup
beforeAll(() => {
  // Set up environment variables
  Object.assign(import.meta.env, mockEnv);
  
  // Mock console methods to reduce noise in tests
  global.console = {
    ...console,
    log: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  };

  // Mock localStorage
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  };
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock
  });

  // Mock sessionStorage
  const sessionStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  };
  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock
  });

  // Mock crypto for secure random generation
  Object.defineProperty(window, 'crypto', {
    value: {
      getRandomValues: vi.fn((arr) => {
        for (let i = 0; i < arr.length; i++) {
          arr[i] = Math.floor(Math.random() * 256);
        }
        return arr;
      }),
      subtle: {
        importKey: vi.fn(),
        sign: vi.fn(),
      }
    }
  });

  // Mock navigator
  Object.defineProperty(window, 'navigator', {
    value: {
      userAgent: 'Mozilla/5.0 (Test Environment)',
      language: 'en-US',
      platform: 'Test'
    }
  });

  // Mock fetch for API calls
  global.fetch = vi.fn();

  // Mock Supabase client
  vi.mock('../lib/supabase/client', () => ({
    supabase: {
      auth: {
        getSession: vi.fn(),
        onAuthStateChange: vi.fn(() => ({
          data: { subscription: { unsubscribe: vi.fn() } }
        })),
        signInWithPassword: vi.fn(),
        signUp: vi.fn(),
        signOut: vi.fn(),
      },
      from: vi.fn(() => ({
        select: vi.fn().mockReturnThis(),
        insert: vi.fn().mockReturnThis(),
        update: vi.fn().mockReturnThis(),
        delete: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn(),
        maybeSingle: vi.fn(),
      })),
    },
    isSupabaseConfigured: true,
  }));

  // Security test helpers
  global.testHelpers = {
    createMaliciousPayload: () => ({
      script: '<script>alert("xss")</script>',
      sql: "'; DROP TABLE users; --",
      jwt: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.malicious.payload',
      password: 'password123',
      token: 'secret-token-123'
    }),
    
    createCleanPayload: () => ({
      name: 'John Doe',
      email: '<EMAIL>',
      message: 'Hello world!',
      phone: '+1234567890'
    }),
    
    createTestUser: (role = 'customer') => ({
      id: 'test-user-123',
      email: '<EMAIL>',
      role,
      isActive: true
    }),
    
    createTestBooking: () => ({
      id: 'booking-123',
      user_id: 'test-user-123',
      service_type: 'residential',
      status: 'pending',
      contact: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890'
      },
      property_details: {
        type: 'house',
        size: 'medium',
        bedrooms: '3',
        bathrooms: '2',
        address: '123 Test St',
        city: 'Test City',
        zipCode: '12345'
      },
      service_details: {
        frequency: 'one-time',
        cleaningType: 'standard',
        addOns: [],
        totalPrice: 150
      },
      schedule: {
        preferredDate: '2024-01-15',
        preferredTime: '10:00'
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
  };
});

// Clean up after all tests
afterAll(() => {
  vi.clearAllMocks();
  vi.resetAllMocks();
});

// Clean up after each test
afterEach(() => {
  cleanup();
  vi.clearAllMocks();
  
  // Clear localStorage and sessionStorage
  localStorage.clear();
  sessionStorage.clear();
  
  // Reset fetch mock
  if (global.fetch) {
    (global.fetch as any).mockClear();
  }
});

// Security-focused test utilities
export const securityTestUtils = {
  /**
   * Test for XSS vulnerabilities
   */
  testXSSProtection: (inputFunction: (input: string) => string) => {
    const xssPayloads = [
      '<script>alert("xss")</script>',
      'javascript:alert("xss")',
      '<img src="x" onerror="alert(\'xss\')">',
      '<svg onload="alert(\'xss\')">',
      '"><script>alert("xss")</script>',
      '\';alert("xss");//'
    ];

    xssPayloads.forEach(payload => {
      const result = inputFunction(payload);
      expect(result).not.toContain('<script>');
      expect(result).not.toContain('javascript:');
      expect(result).not.toContain('onerror=');
      expect(result).not.toContain('onload=');
      expect(result).not.toContain('alert(');
    });
  },

  /**
   * Test for SQL injection vulnerabilities
   */
  testSQLInjectionProtection: (inputFunction: (input: string) => string) => {
    const sqlPayloads = [
      "'; DROP TABLE users; --",
      "' OR '1'='1",
      "'; DELETE FROM users; --",
      "' UNION SELECT * FROM users --",
      "'; INSERT INTO users VALUES ('hacker', 'password'); --"
    ];

    sqlPayloads.forEach(payload => {
      const result = inputFunction(payload);
      expect(result).not.toContain('DROP TABLE');
      expect(result).not.toContain('DELETE FROM');
      expect(result).not.toContain('UNION SELECT');
      expect(result).not.toContain('INSERT INTO');
      expect(result).not.toContain('--');
    });
  },

  /**
   * Test for sensitive data exposure
   */
  testSensitiveDataProtection: (data: any) => {
    const dataString = JSON.stringify(data);
    
    // Should not contain common sensitive patterns
    expect(dataString).not.toMatch(/password.*[:=]\s*[^,}]+/i);
    expect(dataString).not.toMatch(/token.*[:=]\s*[^,}]+/i);
    expect(dataString).not.toMatch(/secret.*[:=]\s*[^,}]+/i);
    expect(dataString).not.toMatch(/key.*[:=]\s*[^,}]+/i);
    
    // Should not contain JWT tokens
    expect(dataString).not.toMatch(/eyJ[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*/);
    
    // Should contain redaction markers instead
    if (dataString.includes('password') || dataString.includes('token') || 
        dataString.includes('secret') || dataString.includes('key')) {
      expect(dataString).toContain('[REDACTED]');
    }
  },

  /**
   * Test rate limiting functionality
   */
  testRateLimit: async (
    rateLimitedFunction: (identifier: string) => Promise<void>,
    identifier: string,
    maxAttempts: number = 5
  ) => {
    // Should allow requests under the limit
    for (let i = 0; i < maxAttempts - 1; i++) {
      await expect(rateLimitedFunction(identifier)).resolves.not.toThrow();
    }

    // Should block requests over the limit
    await expect(rateLimitedFunction(identifier)).rejects.toThrow(/rate limit/i);
  },

  /**
   * Test CSRF protection
   */
  testCSRFProtection: async (
    protectedFunction: (token?: string) => Promise<void>
  ) => {
    // Should reject requests without CSRF token
    await expect(protectedFunction()).rejects.toThrow(/csrf/i);
    
    // Should reject requests with invalid CSRF token
    await expect(protectedFunction('invalid-token')).rejects.toThrow(/csrf/i);
  }
};

// Export test utilities
export { mockEnv };

// Type declarations for global test helpers
declare global {
  var testHelpers: {
    createMaliciousPayload: () => any;
    createCleanPayload: () => any;
    createTestUser: (role?: string) => any;
    createTestBooking: () => any;
  };
}
