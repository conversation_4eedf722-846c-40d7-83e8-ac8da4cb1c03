{"name": "@vitest/ui", "type": "module", "version": "2.1.9", "description": "UI for Vitest", "license": "MIT", "funding": "https://opencollective.com/vitest", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/ui#readme", "repository": {"type": "git", "url": "git+https://github.com/vitest-dev/vitest.git", "directory": "packages/ui"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./reporter": {"types": "./reporter.d.ts", "default": "./dist/reporter.js"}, "./*": "./*"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["*.d.ts", "dist"], "peerDependencies": {"vitest": "2.1.9"}, "dependencies": {"fflate": "^0.8.2", "flatted": "^3.3.1", "pathe": "^1.1.2", "sirv": "^3.0.0", "tinyglobby": "^0.2.10", "tinyrainbow": "^1.2.0", "@vitest/utils": "2.1.9"}, "devDependencies": {"@faker-js/faker": "^9.2.0", "@iconify-json/carbon": "^1.2.4", "@iconify-json/logos": "^1.2.3", "@testing-library/vue": "^8.1.0", "@types/codemirror": "^5.60.15", "@types/d3-force": "^3.0.10", "@types/d3-selection": "^3.0.11", "@types/ws": "^8.5.13", "@unocss/reset": "^0.64.0", "@vitejs/plugin-vue": "^5.1.5", "@vue/test-utils": "^2.4.6", "@vueuse/core": "^11.2.0", "ansi-to-html": "^0.7.2", "birpc": "0.2.19", "codemirror": "^5.65.18", "codemirror-theme-vars": "^0.1.2", "d3-graph-controller": "^3.0.11", "floating-vue": "^5.2.2", "rollup": "^4.25.0", "splitpanes": "^3.1.5", "unocss": "^0.64.0", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vite": "^5.0.0", "vite-plugin-pages": "^0.32.3", "vue": "^3.5.12", "vue-router": "^4.4.5", "vue-virtual-scroller": "2.0.0-beta.8", "@vitest/ws-client": "2.1.9", "@vitest/runner": "2.1.9"}, "scripts": {"build": "rimraf dist && pnpm build:node && pnpm build:client", "build:client": "vite build", "build:node": "rollup -c", "typecheck": "tsc --noEmit", "dev:client": "vite", "dev": "rollup -c --watch --watch.include 'node/**'", "dev:ui": "pnpm run --stream '/^(dev|dev:client)$/'", "test:ui": "vitest --browser"}}