import React, { useState } from 'react';
import { useAuth } from '../../lib/auth/AuthProvider';
import { supabase } from '../../lib/supabase/client';

export function DatabaseTest() {
  const { user } = useAuth();
  const [testResult, setTestResult] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const testDatabaseConnection = async () => {
    setIsLoading(true);
    setTestResult('Testing database connection...\n');

    try {
      // Test 0: Check if Supabase is configured
      if (!supabase) {
        setTestResult(prev => prev + '❌ Supabase client not configured\n');
        setIsLoading(false);
        return;
      }
      setTestResult(prev => prev + '✅ Supabase client configured\n');

      // Test 1: Check if user is authenticated
      if (!user) {
        setTestResult(prev => prev + '❌ No user authenticated\n');
        setIsLoading(false);
        return;
      }
      setTestResult(prev => prev + `✅ User authenticated: ${user.id}\n`);

      // Test 2: Try to fetch existing bookings
      const { data: existingBookings, error: fetchError } = await supabase
        .from('booking_forms')
        .select('*')
        .eq('user_id', user.id);

      if (fetchError) {
        setTestResult(prev => prev + `❌ Error fetching bookings: ${fetchError.message}\n`);
      } else {
        setTestResult(prev => prev + `✅ Successfully fetched ${existingBookings?.length || 0} existing bookings\n`);
      }

      // Test 3: Try to create a test booking
      const testBookingData = {
        user_id: user.id,
        service_type: 'deep',
        status: 'pending',
        contact: {
          firstName: 'Test',
          lastName: 'User',
          email: user.email || '<EMAIL>',
          phone: '555-1234'
        },
        property_details: {
          type: 'house',
          size: 'medium',
          bedrooms: '3',
          bathrooms: '2',
          address: '123 Test St',
          city: 'Test City',
          zipCode: '12345'
        },
        service_details: {
          frequency: 'weekly',
          cleaningType: 'standard',
          addOns: [],
          isTest: true,
          totalPrice: 150.00,
          specialInstructions: 'This is a test booking'
        },
        schedule: {
          preferredDate: '2025-02-10',
          preferredTime: '10:00 AM'
        }
      };

      setTestResult(prev => prev + 'Attempting to create test booking...\n');

      const { data: savedBooking, error: saveError } = await supabase
        .from('booking_forms')
        .insert([testBookingData])
        .select()
        .single();

      if (saveError) {
        setTestResult(prev => prev + `❌ Error saving test booking: ${saveError.message}\n`);
        setTestResult(prev => prev + `Error details: ${JSON.stringify(saveError, null, 2)}\n`);
      } else {
        setTestResult(prev => prev + `✅ Test booking saved successfully!\n`);
        setTestResult(prev => prev + `Booking ID: ${savedBooking.id}\n`);

        // Test 4: Try to fetch the booking we just created
        const { data: fetchedBooking, error: refetchError } = await supabase
          .from('booking_forms')
          .select('*')
          .eq('id', savedBooking.id)
          .single();

        if (refetchError) {
          setTestResult(prev => prev + `❌ Error refetching test booking: ${refetchError.message}\n`);
        } else {
          setTestResult(prev => prev + `✅ Successfully refetched test booking: ${fetchedBooking.service_type}\n`);
        }

        // Clean up: Delete the test booking
        const { error: deleteError } = await supabase
          .from('booking_forms')
          .delete()
          .eq('id', savedBooking.id);

        if (deleteError) {
          setTestResult(prev => prev + `⚠️ Could not delete test booking: ${deleteError.message}\n`);
        } else {
          setTestResult(prev => prev + `✅ Test booking cleaned up successfully\n`);
        }
      }

    } catch (error) {
      setTestResult(prev => prev + `❌ Unexpected error: ${error}\n`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl p-6 m-4">
      <h2 className="text-xl font-bold text-white mb-4">Database Connection Test</h2>
      
      <button
        onClick={testDatabaseConnection}
        disabled={isLoading}
        className="bg-green-500 hover:bg-green-600 disabled:opacity-50 text-white px-4 py-2 rounded-lg mb-4"
      >
        {isLoading ? 'Testing...' : 'Run Database Test'}
      </button>

      {testResult && (
        <div className="bg-black/50 rounded-lg p-4">
          <pre className="text-sm text-white whitespace-pre-wrap font-mono">
            {testResult}
          </pre>
        </div>
      )}
    </div>
  );
} 