import { createClient } from '@supabase/supabase-js';
import { getEnvironmentConfig } from '../config/environment';

// Get validated environment configuration
const envConfig = getEnvironmentConfig();

// Export configuration status
export const isSupabaseConfigured = envConfig.supabase.isConfigured;

if (!isSupabaseConfigured) {
  console.error('❌ Supabase configuration is missing or invalid. Please check your environment variables.');
  console.error('Required: VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY');
}

// Create and export the Supabase client with validated configuration
export const supabase = isSupabaseConfigured
  ? createClient(envConfig.supabase.url, envConfig.supabase.anonKey, {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true
      },
      global: {
        headers: {
          'X-Client-Info': 'empirepro-cleaning-app'
        }
      }
    })
  : null;

// Helper function to check if client is available
export function getSupabaseClient() {
  if (!supabase) {
    throw new Error(
      'Supabase client is not configured. Please check your environment variables and ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set correctly.'
    );
  }
  return supabase;
}

// Helper function to check network connectivity with Supabase
export async function checkSupabaseConnection() {
  if (!isSupabaseConfigured) {
    throw new Error('Supabase is not configured. Please check your environment variables.');
  }
  
  try {
    // Use a simple query to test the connection instead of direct fetch
    const { error } = await getSupabaseClient()
      .from('profiles')
      .select('id')
      .limit(1);
      
    if (error) {
      throw new Error('Could not connect to Supabase: ' + error.message);
    }
    
    return true;
  } catch (error) {
    throw new Error('Failed to connect to Supabase. Please check your internet connection and configuration.');
  }
}