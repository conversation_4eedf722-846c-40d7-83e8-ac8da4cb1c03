# Square Payment Environment Setup

## Frontend Environment Variables (.env.local)
Create a `.env.local` file in your project root with:

```bash
# Square Payment Configuration
VITE_SQUARE_APPLICATION_ID=sandbox-sq0idb-YOUR_APPLICATION_ID_HERE
VITE_SQUARE_ACCESS_TOKEN=YOUR_ACCESS_TOKEN_HERE
VITE_SQUARE_LOCATION_ID=YOUR_LOCATION_ID_HERE
VITE_SQUARE_ENVIRONMENT=sandbox

# Public URL for redirects
VITE_PUBLIC_URL=http://localhost:5173
```

## Supabase Edge Function Environment Variables
Configure in Supabase Dashboard under Settings > Edge Functions:

```bash
SQUARE_ACCESS_TOKEN=YOUR_ACCESS_TOKEN_HERE
SQUARE_LOCATION_ID=YOUR_LOCATION_ID_HERE
SQUARE_ENVIRONMENT=sandbox
PUBLIC_URL=http://localhost:5173
```

## Production Environment Variables
For production, replace `sandbox` with `production` and use production credentials:

```bash
VITE_SQUARE_ENVIRONMENT=production
SQUARE_ENVIRONMENT=production
VITE_PUBLIC_URL=https://your-domain.com
PUBLIC_URL=https://your-domain.com
```

## How to Get Square Credentials
1. Visit https://developer.squareup.com/
2. Create/login to your developer account
3. Create a new Application
4. Go to Credentials tab
5. Copy Application ID, Access Token, and Location ID 