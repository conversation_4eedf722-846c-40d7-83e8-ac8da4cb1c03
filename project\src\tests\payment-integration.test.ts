/**
 * Square Payment Integration Tests
 * 
 * Comprehensive end-to-end testing of Square payment integration
 * including payment processing, webhook handling, database operations,
 * and security validation.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { processResidentialPayment } from '../lib/api/paymentService';
import { getSquareConfig, isSquareConfigured } from '../lib/square/config';
import { supabase } from '../lib/supabase/client';

// Mock data for testing
const mockFormData = {
  serviceType: 'residential',
  contact: {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+1234567890'
  },
  propertyDetails: {
    type: 'house',
    size: 'medium',
    bedrooms: '3',
    bathrooms: '2',
    address: '123 Test St',
    city: 'Test City',
    zipCode: '12345'
  },
  serviceDetails: {
    frequency: 'one-time',
    cleaningType: 'standard',
    addOns: [],
    totalPrice: 150
  },
  schedule: {
    preferredDate: '2024-01-15',
    preferredTime: '10:00'
  }
};

const mockUser = {
  id: 'test-user-123',
  email: '<EMAIL>',
  role: 'customer'
};

const mockPaymentResponse = {
  id: 'payment-link-123',
  url: 'https://square.link/payment-123',
  orderId: 'order-123',
  amount: 15000, // $150.00 in cents
  description: 'Residential Cleaning Service'
};

const mockWebhookEvent = {
  type: 'payment.updated',
  data: {
    id: 'payment-123',
    order_id: 'order-123',
    status: 'COMPLETED',
    amount_money: {
      amount: 15000,
      currency: 'USD'
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
};

describe('Square Payment Integration', () => {
  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Mock environment variables
    vi.stubEnv('VITE_SQUARE_APPLICATION_ID', 'sandbox-sq0idb-test-app-id');
    vi.stubEnv('VITE_SQUARE_ACCESS_TOKEN', 'EAAAl-test-access-token');
    vi.stubEnv('VITE_SQUARE_LOCATION_ID', 'test-location-id');
    vi.stubEnv('VITE_SQUARE_ENVIRONMENT', 'sandbox');
  });

  afterEach(() => {
    vi.unstubAllEnvs();
  });

  describe('Configuration Validation', () => {
    it('should validate Square configuration correctly', () => {
      const config = getSquareConfig();
      
      expect(config.isConfigured).toBe(true);
      expect(config.applicationId).toBe('sandbox-sq0idb-test-app-id');
      expect(config.accessToken).toBe('EAAAl-test-access-token');
      expect(config.locationId).toBe('test-location-id');
      expect(config.environment).toBe('sandbox');
    });

    it('should detect missing configuration', () => {
      vi.stubEnv('VITE_SQUARE_APPLICATION_ID', '');
      
      const config = getSquareConfig();
      expect(config.isConfigured).toBe(false);
    });

    it('should validate environment-specific settings', () => {
      // Test sandbox configuration
      vi.stubEnv('VITE_SQUARE_ENVIRONMENT', 'sandbox');
      vi.stubEnv('VITE_SQUARE_APPLICATION_ID', 'sandbox-sq0idb-test');
      
      expect(isSquareConfigured()).toBe(true);
      
      // Test production configuration
      vi.stubEnv('VITE_SQUARE_ENVIRONMENT', 'production');
      vi.stubEnv('VITE_SQUARE_APPLICATION_ID', 'sq0idb-production');
      
      expect(isSquareConfigured()).toBe(true);
    });
  });

  describe('Payment Processing', () => {
    beforeEach(() => {
      // Mock Supabase functions
      vi.mocked(supabase.functions.invoke).mockResolvedValue({
        data: mockPaymentResponse,
        error: null
      });
    });

    it('should process payment successfully', async () => {
      const result = await processResidentialPayment(mockFormData, 150, mockUser);
      
      expect(result).toBeDefined();
      expect(result.url).toBe(mockPaymentResponse.url);
      expect(result.id).toBe(mockPaymentResponse.id);
      
      // Verify Supabase function was called with correct parameters
      expect(supabase.functions.invoke).toHaveBeenCalledWith('create-payment-link', {
        body: {
          amount: 15000, // $150 in cents
          description: 'Residential Cleaning Service',
          formData: {
            ...mockFormData,
            user_id: mockUser.id,
            serviceType: 'residential'
          }
        }
      });
    });

    it('should handle payment processing errors', async () => {
      const mockError = { message: 'Payment processing failed' };
      vi.mocked(supabase.functions.invoke).mockResolvedValue({
        data: null,
        error: mockError
      });

      await expect(processResidentialPayment(mockFormData, 150, mockUser))
        .rejects.toThrow('Payment processing failed');
    });

    it('should validate payment amount', async () => {
      // Test with invalid amount
      await expect(processResidentialPayment(mockFormData, -50, mockUser))
        .rejects.toThrow();
      
      await expect(processResidentialPayment(mockFormData, 0, mockUser))
        .rejects.toThrow();
    });

    it('should handle guest user payments', async () => {
      const result = await processResidentialPayment(mockFormData, 150, null);
      
      expect(supabase.functions.invoke).toHaveBeenCalledWith('create-payment-link', {
        body: {
          amount: 15000,
          description: 'Residential Cleaning Service',
          formData: {
            ...mockFormData,
            user_id: null,
            serviceType: 'residential'
          }
        }
      });
    });

    it('should sanitize form data before processing', async () => {
      const maliciousFormData = {
        ...mockFormData,
        contact: {
          ...mockFormData.contact,
          firstName: '<script>alert("xss")</script>John',
          email: '<EMAIL>\'; DROP TABLE users; --'
        }
      };

      await processResidentialPayment(maliciousFormData, 150, mockUser);
      
      const callArgs = vi.mocked(supabase.functions.invoke).mock.calls[0][1];
      const sanitizedData = callArgs.body.formData;
      
      expect(sanitizedData.contact.firstName).not.toContain('<script>');
      expect(sanitizedData.contact.email).not.toContain('DROP TABLE');
    });
  });

  describe('Database Operations', () => {
    beforeEach(() => {
      // Mock Supabase database operations
      vi.mocked(supabase.from).mockReturnValue({
        insert: vi.fn().mockReturnThis(),
        update: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: null, error: null })
      } as any);
    });

    it('should create payment record in database', async () => {
      await processResidentialPayment(mockFormData, 150, mockUser);
      
      // Verify payment record creation would be handled by the Edge Function
      expect(supabase.functions.invoke).toHaveBeenCalled();
    });

    it('should handle database connection errors', async () => {
      // Mock Supabase as null (not configured)
      vi.mocked(supabase).mockReturnValue(null);
      
      await expect(processResidentialPayment(mockFormData, 150, mockUser))
        .rejects.toThrow('Database client is not initialized');
    });

    it('should validate database schema compliance', () => {
      // Test that our data structure matches expected database schema
      const paymentRecord = {
        id: 'uuid',
        user_id: mockUser.id,
        service_type: 'residential',
        amount: 150.00,
        payment_link_id: 'payment-link-123',
        payment_link_url: 'https://square.link/payment-123',
        square_payment_id: null,
        order_id: 'order-123',
        booking_id: 'booking-123',
        status: 'pending',
        metadata: {},
        created_at: expect.any(String),
        updated_at: expect.any(String)
      };

      // Verify all required fields are present
      expect(paymentRecord).toHaveProperty('id');
      expect(paymentRecord).toHaveProperty('user_id');
      expect(paymentRecord).toHaveProperty('service_type');
      expect(paymentRecord).toHaveProperty('amount');
      expect(paymentRecord).toHaveProperty('status');
    });
  });

  describe('Webhook Security', () => {
    it('should validate webhook signature', () => {
      // Test webhook signature validation logic
      const webhookBody = JSON.stringify(mockWebhookEvent);
      const secretKey = 'test-webhook-secret';
      
      // This would be tested in the actual webhook handler
      expect(webhookBody).toBeDefined();
      expect(secretKey).toBeDefined();
    });

    it('should handle webhook event types correctly', () => {
      const supportedEvents = ['payment.created', 'payment.updated'];
      
      supportedEvents.forEach(eventType => {
        const event = { ...mockWebhookEvent, type: eventType };
        expect(event.type).toBe(eventType);
      });
    });

    it('should validate webhook payload structure', () => {
      expect(mockWebhookEvent).toHaveProperty('type');
      expect(mockWebhookEvent).toHaveProperty('data');
      expect(mockWebhookEvent.data).toHaveProperty('id');
      expect(mockWebhookEvent.data).toHaveProperty('status');
    });
  });

  describe('Error Handling', () => {
    it('should handle Square API errors gracefully', async () => {
      const squareError = {
        message: 'INVALID_REQUEST_ERROR: Invalid payment amount',
        code: 'INVALID_REQUEST_ERROR'
      };

      vi.mocked(supabase.functions.invoke).mockResolvedValue({
        data: null,
        error: squareError
      });

      await expect(processResidentialPayment(mockFormData, 150, mockUser))
        .rejects.toThrow('INVALID_REQUEST_ERROR');
    });

    it('should handle network errors', async () => {
      vi.mocked(supabase.functions.invoke).mockRejectedValue(
        new Error('Network error: Unable to connect to Square API')
      );

      await expect(processResidentialPayment(mockFormData, 150, mockUser))
        .rejects.toThrow('Network error');
    });

    it('should handle timeout errors', async () => {
      vi.mocked(supabase.functions.invoke).mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), 100)
        )
      );

      await expect(processResidentialPayment(mockFormData, 150, mockUser))
        .rejects.toThrow('Request timeout');
    });
  });

  describe('Security Validation', () => {
    it('should prevent amount manipulation', async () => {
      // Test that amount is properly validated and converted
      const testCases = [
        { input: 150, expected: 15000 },
        { input: 99.99, expected: 9999 },
        { input: 0.01, expected: 1 }
      ];

      for (const testCase of testCases) {
        await processResidentialPayment(mockFormData, testCase.input, mockUser);
        
        const callArgs = vi.mocked(supabase.functions.invoke).mock.calls.slice(-1)[0][1];
        expect(callArgs.body.amount).toBe(testCase.expected);
      }
    });

    it('should validate user permissions', async () => {
      // Test that user can only create payments for themselves
      const result = await processResidentialPayment(mockFormData, 150, mockUser);
      
      const callArgs = vi.mocked(supabase.functions.invoke).mock.calls[0][1];
      expect(callArgs.body.formData.user_id).toBe(mockUser.id);
    });

    it('should sanitize payment descriptions', async () => {
      const maliciousFormData = {
        ...mockFormData,
        serviceType: '<script>alert("xss")</script>residential'
      };

      await processResidentialPayment(maliciousFormData, 150, mockUser);
      
      const callArgs = vi.mocked(supabase.functions.invoke).mock.calls[0][1];
      expect(callArgs.body.description).not.toContain('<script>');
    });
  });

  describe('Integration Flow', () => {
    it('should complete full payment flow', async () => {
      // Step 1: Process payment
      const paymentResult = await processResidentialPayment(mockFormData, 150, mockUser);
      expect(paymentResult.url).toBeDefined();
      
      // Step 2: Verify database operations would be called
      expect(supabase.functions.invoke).toHaveBeenCalledWith('create-payment-link', expect.any(Object));
      
      // Step 3: Simulate webhook processing (would happen in Edge Function)
      // This validates the webhook event structure
      expect(mockWebhookEvent.type).toBe('payment.updated');
      expect(mockWebhookEvent.data.status).toBe('COMPLETED');
    });

    it('should handle payment cancellation', async () => {
      const cancelledEvent = {
        ...mockWebhookEvent,
        data: {
          ...mockWebhookEvent.data,
          status: 'CANCELED'
        }
      };

      expect(cancelledEvent.data.status).toBe('CANCELED');
    });

    it('should handle payment failure', async () => {
      const failedEvent = {
        ...mockWebhookEvent,
        data: {
          ...mockWebhookEvent.data,
          status: 'FAILED'
        }
      };

      expect(failedEvent.data.status).toBe('FAILED');
    });
  });
});
