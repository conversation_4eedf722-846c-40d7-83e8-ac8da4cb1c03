#!/usr/bin/env node

/**
 * Environment Setup Script
 * 
 * This script helps set up the environment configuration for the Empire Pro
 * Cleaning Business application. It validates environment variables and
 * provides guidance for proper configuration.
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  bold: '\x1b[1m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(message.toUpperCase(), 'bold');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Check if .env file exists
function checkEnvFile() {
  const envPath = path.join(process.cwd(), '.env');
  const envExamplePath = path.join(process.cwd(), '.env.example');
  
  if (!fs.existsSync(envPath)) {
    logWarning('.env file not found');
    
    if (fs.existsSync(envExamplePath)) {
      logInfo('Copying .env.example to .env...');
      fs.copyFileSync(envExamplePath, envPath);
      logSuccess('.env file created from .env.example');
      logWarning('Please update the .env file with your actual values');
    } else {
      logError('.env.example file not found');
      return false;
    }
  } else {
    logSuccess('.env file found');
  }
  
  return true;
}

// Parse .env file
function parseEnvFile() {
  const envPath = path.join(process.cwd(), '.env');
  
  if (!fs.existsSync(envPath)) {
    return {};
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envVars = {};
  
  envContent.split('\n').forEach(line => {
    const trimmedLine = line.trim();
    if (trimmedLine && !trimmedLine.startsWith('#')) {
      const [key, ...valueParts] = trimmedLine.split('=');
      if (key && valueParts.length > 0) {
        envVars[key.trim()] = valueParts.join('=').trim();
      }
    }
  });
  
  return envVars;
}

// Validate environment variables
function validateEnvironment() {
  const envVars = parseEnvFile();
  const issues = [];
  const warnings = [];
  
  // Supabase validation
  if (!envVars.VITE_SUPABASE_URL) {
    issues.push('VITE_SUPABASE_URL is missing');
  } else if (!envVars.VITE_SUPABASE_URL.startsWith('https://')) {
    issues.push('VITE_SUPABASE_URL must start with https://');
  } else if (!envVars.VITE_SUPABASE_URL.includes('.supabase.co')) {
    issues.push('VITE_SUPABASE_URL must be a valid Supabase URL');
  }
  
  if (!envVars.VITE_SUPABASE_ANON_KEY) {
    issues.push('VITE_SUPABASE_ANON_KEY is missing');
  } else if (!envVars.VITE_SUPABASE_ANON_KEY.startsWith('eyJ')) {
    issues.push('VITE_SUPABASE_ANON_KEY appears to be invalid');
  }
  
  // Square validation
  if (!envVars.VITE_SQUARE_APPLICATION_ID) {
    warnings.push('VITE_SQUARE_APPLICATION_ID is missing (payment features will not work)');
  } else if (envVars.VITE_SQUARE_APPLICATION_ID.includes('your-')) {
    warnings.push('VITE_SQUARE_APPLICATION_ID contains placeholder text');
  }
  
  if (!envVars.VITE_SQUARE_ACCESS_TOKEN) {
    warnings.push('VITE_SQUARE_ACCESS_TOKEN is missing (payment features will not work)');
  } else if (envVars.VITE_SQUARE_ACCESS_TOKEN.includes('your-')) {
    warnings.push('VITE_SQUARE_ACCESS_TOKEN contains placeholder text');
  }
  
  if (!envVars.VITE_SQUARE_LOCATION_ID) {
    warnings.push('VITE_SQUARE_LOCATION_ID is missing (payment features will not work)');
  } else if (envVars.VITE_SQUARE_LOCATION_ID.includes('your-')) {
    warnings.push('VITE_SQUARE_LOCATION_ID contains placeholder text');
  }
  
  // Webhook validation
  if (!envVars.VITE_N8N_WEBHOOK_URL) {
    warnings.push('VITE_N8N_WEBHOOK_URL is missing (webhook notifications will not work)');
  } else if (envVars.VITE_N8N_WEBHOOK_URL.includes('your-')) {
    warnings.push('VITE_N8N_WEBHOOK_URL contains placeholder text');
  }
  
  return { issues, warnings };
}

// Main setup function
function setupEnvironment() {
  logHeader('Empire Pro Cleaning - Environment Setup');
  
  logInfo('Checking environment configuration...\n');
  
  // Check .env file
  if (!checkEnvFile()) {
    logError('Failed to set up .env file');
    process.exit(1);
  }
  
  // Validate environment
  const { issues, warnings } = validateEnvironment();
  
  // Report results
  logHeader('Validation Results');
  
  if (issues.length === 0) {
    logSuccess('All critical environment variables are configured!');
  } else {
    logError('Critical issues found:');
    issues.forEach(issue => logError(`  • ${issue}`));
  }
  
  if (warnings.length > 0) {
    log('\n');
    logWarning('Warnings:');
    warnings.forEach(warning => logWarning(`  • ${warning}`));
  }
  
  // Provide guidance
  logHeader('Next Steps');
  
  if (issues.length > 0) {
    logInfo('1. Update your .env file with the correct values');
    logInfo('2. Get Supabase credentials from: https://supabase.com/dashboard');
    logInfo('3. Get Square credentials from: https://developer.squareup.com/');
    logInfo('4. Run this script again to validate');
  } else if (warnings.length > 0) {
    logInfo('1. Consider setting up optional features (Square payments, webhooks)');
    logInfo('2. Your application should work with basic functionality');
  } else {
    logSuccess('Your environment is fully configured!');
    logInfo('You can now run: npm run dev');
  }
  
  log('\n');
}

// Run the setup
if (require.main === module) {
  setupEnvironment();
}

module.exports = { setupEnvironment, validateEnvironment, parseEnvFile };
