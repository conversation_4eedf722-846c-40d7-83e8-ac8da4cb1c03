import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './errorHandler';

// Input sanitization utilities
export class InputSanitizer {
  // Sanitize HTML to prevent XSS attacks
  static sanitizeHtml(input: string): string {
    if (!input) return '';

    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  // Sanitize SQL input to prevent injection
  static sanitizeSql(input: string): string {
    if (!input) return '';

    // Remove or escape dangerous SQL characters
    return input
      .replace(/'/g, "''")  // Escape single quotes
      .replace(/;/g, '')    // Remove semicolons
      .replace(/--/g, '')   // Remove SQL comments
      .replace(/\/\*/g, '') // Remove block comment start
      .replace(/\*\//g, '') // Remove block comment end
      .replace(/xp_/gi, '') // Remove extended procedures
      .replace(/sp_/gi, ''); // Remove stored procedures
  }

  // Sanitize general text input
  static sanitizeText(input: string, maxLength: number = 1000): string {
    if (!input) return '';

    return input
      .trim()
      .slice(0, maxLength)
      .replace(/[\x00-\x1F\x7F]/g, '') // Remove control characters
      .replace(/[<>]/g, ''); // Remove angle brackets
  }

  // Sanitize email input
  static sanitizeEmail(input: string): string {
    if (!input) return '';

    return input
      .trim()
      .toLowerCase()
      .slice(0, 254) // RFC 5321 limit
      .replace(/[^a-z0-9@._-]/g, ''); // Only allow valid email characters
  }

  // Sanitize phone number
  static sanitizePhone(input: string): string {
    if (!input) return '';

    return input
      .replace(/[^0-9+\-\(\)\s]/g, '') // Only allow valid phone characters
      .trim()
      .slice(0, 20); // Reasonable phone number length
  }

  // Sanitize numeric input
  static sanitizeNumber(input: string | number): number | null {
    if (input === null || input === undefined || input === '') return null;

    const num = typeof input === 'string' ? parseFloat(input.replace(/[^0-9.-]/g, '')) : input;
    return isNaN(num) ? null : num;
  }
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  email?: boolean;
  phone?: boolean;
  custom?: (value: any) => boolean;
  customMessage?: string;
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export class FormValidator {
  static validateField(field: string, value: any, rules: ValidationRule): ValidationError | null {
    // Sanitize input first
    let sanitizedValue = value;
    if (typeof value === 'string') {
      sanitizedValue = InputSanitizer.sanitizeText(value);
    }

    // Required check
    if (rules.required && (!sanitizedValue || sanitizedValue.toString().trim() === '')) {
      const error = ErrorHandler.handleFormValidationError(field, sanitizedValue, 'required');
      return {
        field,
        message: error.message,
        code: error.code
      };
    }

    // Skip other validations if field is empty and not required
    if (!sanitizedValue || sanitizedValue.toString().trim() === '') {
      return null;
    }

    const stringValue = sanitizedValue.toString().trim();

    // Email validation
    if (rules.email) {
      const sanitizedEmail = InputSanitizer.sanitizeEmail(stringValue);
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(sanitizedEmail)) {
        const error = ErrorHandler.handleFormValidationError(field, sanitizedEmail, 'email');
        return {
          field,
          message: error.message,
          code: error.code
        };
      }
    }

    // Phone validation
    if (rules.phone) {
      const sanitizedPhone = InputSanitizer.sanitizePhone(stringValue);
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
      const cleanPhone = sanitizedPhone.replace(/[\s\-\(\)\.]/g, '');
      if (!phoneRegex.test(cleanPhone) || cleanPhone.length < 10) {
        const error = ErrorHandler.handleFormValidationError(field, sanitizedPhone, 'phone');
        return {
          field,
          message: error.message,
          code: error.code
        };
      }
    }

    // Min length validation
    if (rules.minLength && stringValue.length < rules.minLength) {
      const error = ErrorHandler.handleFormValidationError(field, rules.minLength, 'minLength');
      return {
        field,
        message: error.message,
        code: error.code
      };
    }

    // Max length validation
    if (rules.maxLength && stringValue.length > rules.maxLength) {
      const error = ErrorHandler.handleFormValidationError(field, rules.maxLength, 'maxLength');
      return {
        field,
        message: error.message,
        code: error.code
      };
    }

    // Pattern validation
    if (rules.pattern && !rules.pattern.test(stringValue)) {
      const error = ErrorHandler.handleFormValidationError(field, value, 'pattern');
      return {
        field,
        message: error.message,
        code: error.code
      };
    }

    // Custom validation
    if (rules.custom && !rules.custom(value)) {
      const message = rules.customMessage || `${field} is invalid`;
      return {
        field,
        message,
        code: 'CUSTOM_VALIDATION_ERROR'
      };
    }

    return null;
  }

  static validateForm(formData: Record<string, any>, validationRules: Record<string, ValidationRule>): ValidationError[] {
    const errors: ValidationError[] = [];

    for (const [field, rules] of Object.entries(validationRules)) {
      const value = formData[field];
      const error = this.validateField(field, value, rules);
      if (error) {
        errors.push(error);
      }
    }

    return errors;
  }

  static isFormValid(formData: Record<string, any>, validationRules: Record<string, ValidationRule>): boolean {
    return this.validateForm(formData, validationRules).length === 0;
  }

  // Common validation rules
  static commonRules = {
    required: { required: true },
    email: { required: true, email: true },
    phone: { required: true, phone: true },
    name: { required: true, minLength: 2, maxLength: 50 },
    address: { required: true, minLength: 5, maxLength: 200 },
    zipCode: { 
      required: true, 
      pattern: /^\d{5}(-\d{4})?$/,
      customMessage: 'Please enter a valid ZIP code (12345 or 12345-6789)'
    },
    password: {
      required: true,
      minLength: 8,
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      customMessage: 'Password must contain at least 8 characters with uppercase, lowercase, number, and special character'
    }
  };

  // Service-specific validation rules
  static serviceValidationRules = {
    carpetCleaning: {
      numberOfRooms: { required: true },
      carpetType: { required: true },
      preferredDate: { required: true },
      preferredTime: { required: true },
      firstName: FormValidator.commonRules.name,
      lastName: FormValidator.commonRules.name,
      email: FormValidator.commonRules.email,
      phone: FormValidator.commonRules.phone,
      address: FormValidator.commonRules.address,
      city: { required: true, minLength: 2, maxLength: 50 },
      zipCode: FormValidator.commonRules.zipCode
    },
    deepCleaning: {
      propertyType: { required: true },
      propertySize: { required: true },
      numberOfBedrooms: { required: true },
      numberOfBathrooms: { required: true },
      preferredDate: { required: true },
      preferredTime: { required: true },
      firstName: FormValidator.commonRules.name,
      lastName: FormValidator.commonRules.name,
      email: FormValidator.commonRules.email,
      phone: FormValidator.commonRules.phone,
      address: FormValidator.commonRules.address,
      city: { required: true, minLength: 2, maxLength: 50 },
      zipCode: FormValidator.commonRules.zipCode
    },
    eventCleaning: {
      eventType: { required: true },
      eventSize: { required: true },
      eventDate: { required: true },
      setupTime: { required: true },
      firstName: FormValidator.commonRules.name,
      lastName: FormValidator.commonRules.name,
      email: FormValidator.commonRules.email,
      phone: FormValidator.commonRules.phone,
      address: FormValidator.commonRules.address,
      city: { required: true, minLength: 2, maxLength: 50 },
      zipCode: FormValidator.commonRules.zipCode
    }
  };

  // Get validation rules for a specific service
  static getServiceRules(serviceType: string): Record<string, ValidationRule> {
    return this.serviceValidationRules[serviceType as keyof typeof this.serviceValidationRules] || {};
  }
} 