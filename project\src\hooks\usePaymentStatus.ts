import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../lib/supabase/client';

interface PaymentStatus {
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  paymentId?: string;
  bookingId?: string;
  error?: string;
  isLoading: boolean;
}

interface PaymentRecord {
  id: string;
  status: string;
  booking_id: string;
  square_payment_id: string;
}

export function usePaymentStatus(paymentLinkId: string | null) {
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus>({
    status: 'pending',
    isLoading: false,
  });

  const checkStatus = useCallback(async () => {
    if (!paymentLinkId) return;

    try {
      setPaymentStatus(prev => ({ ...prev, isLoading: true, error: undefined }));

      if (!supabase) {
        throw new Error('Database client is not initialized');
      }
      
      // Get additional payment details from database
      const { data: paymentRecord, error } = await supabase
        .from('payment_records')
        .select('id, status, booking_id, square_payment_id')
        .eq('payment_link_id', paymentLinkId)
        .single();

      if (error) {
        console.error('Error fetching payment record:', error);
        setPaymentStatus(prev => ({
          ...prev,
          error: 'Failed to check payment status',
          isLoading: false,
        }));
        return;
      }

      setPaymentStatus({
        status: paymentRecord.status as PaymentStatus['status'],
        paymentId: paymentRecord.square_payment_id,
        bookingId: paymentRecord.booking_id,
        isLoading: false,
      });

    } catch (error) {
      console.error('Error checking payment status:', error);
      setPaymentStatus(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to check payment status',
        isLoading: false,
      }));
    }
  }, [paymentLinkId]);

  // Set up real-time subscription to payment status changes
  useEffect(() => {
    if (!paymentLinkId || !supabase) return;

    const subscription = supabase
      .channel('payment-status')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'payment_records',
          filter: `payment_link_id=eq.${paymentLinkId}`,
        },
        (payload) => {
          console.log('Payment status updated:', payload);
          const newRecord = payload.new as PaymentRecord;
          setPaymentStatus(prev => ({
            ...prev,
            status: newRecord.status as PaymentStatus['status'],
            paymentId: newRecord.square_payment_id,
            bookingId: newRecord.booking_id,
          }));
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [paymentLinkId]);

  // Initial status check
  useEffect(() => {
    checkStatus();
  }, [checkStatus]);

  return {
    paymentStatus,
    checkStatus,
    isCompleted: paymentStatus.status === 'completed',
    isFailed: paymentStatus.status === 'failed' || paymentStatus.status === 'cancelled',
    isProcessing: paymentStatus.status === 'processing',
  };
} 