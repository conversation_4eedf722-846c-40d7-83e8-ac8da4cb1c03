import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "npm:@supabase/supabase-js";
import { crypto } from "https://deno.land/std@0.168.0/crypto/mod.ts";

// TypeScript interfaces for webhook data
interface SquarePaymentData {
  id: string;
  status: string;
  order_id: string;
  amount_money?: {
    amount: number;
    currency: string;
  };
  created_at?: string;
  updated_at?: string;
}

interface WebhookEvent {
  type: string;
  data: SquarePaymentData;
  event_id?: string;
  merchant_id?: string;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
const supabaseKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// Square webhook signature verification
const webhookSignatureKey = Deno.env.get("SQUARE_WEBHOOK_SIGNATURE_KEY") || "";

// Function to verify webhook signature
async function verifyWebhookSignature(body: string, signature: string, key: string): Promise<boolean> {
  if (!key || !signature) {
    console.warn("Missing webhook signature key or signature header");
    return false;
  }

  try {
    // Remove 'sha256=' prefix if present
    const cleanSignature = signature.replace(/^sha256=/, '');

    // Create HMAC-SHA256 hash
    const encoder = new TextEncoder();
    const keyData = encoder.encode(key);
    const bodyData = encoder.encode(body);

    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    );

    const signature_buffer = await crypto.subtle.sign('HMAC', cryptoKey, bodyData);
    const expectedSignature = Array.from(new Uint8Array(signature_buffer))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');

    // Compare signatures using constant-time comparison
    return constantTimeCompare(cleanSignature, expectedSignature);
  } catch (error) {
    console.error("Error verifying webhook signature:", error);
    return false;
  }
}

// Constant-time string comparison to prevent timing attacks
function constantTimeCompare(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false;
  }

  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }

  return result === 0;
}

serve(async (req) => {
  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Square-Signature",
  };

  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers: corsHeaders,
      status: 204,
    });
  }

  // Only allow POST requests
  if (req.method !== "POST") {
    return new Response(
      JSON.stringify({ error: "Method not allowed" }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 405,
      }
    );
  }

  try {
    const body = await req.text();
    console.log("Webhook received:", body);

    // Verify webhook signature (REQUIRED for security)
    const signature = req.headers.get("X-Square-Signature");

    // Always verify signature if key is configured
    if (webhookSignatureKey) {
      if (!signature) {
        console.error("Missing webhook signature header");
        return new Response(
          JSON.stringify({ error: "Missing signature header" }),
          {
            headers: { ...corsHeaders, "Content-Type": "application/json" },
            status: 401,
          }
        );
      }

      const isValidSignature = await verifyWebhookSignature(body, signature, webhookSignatureKey);
      if (!isValidSignature) {
        console.error("Invalid webhook signature");
        return new Response(
          JSON.stringify({ error: "Invalid signature" }),
          {
            headers: { ...corsHeaders, "Content-Type": "application/json" },
            status: 401,
          }
        );
      }

      console.log("Webhook signature verified successfully");
    } else {
      console.warn("⚠️ SECURITY WARNING: Webhook signature verification is disabled. Set SQUARE_WEBHOOK_SIGNATURE_KEY environment variable.");
    }

    const webhookEvent = JSON.parse(body) as WebhookEvent;
    console.log("Webhook event:", webhookEvent);

    // Handle different webhook event types
    if (webhookEvent.type === "payment.created") {
      await handlePaymentCreated(webhookEvent.data);
    } else if (webhookEvent.type === "payment.updated") {
      await handlePaymentUpdated(webhookEvent.data);
    } else {
      console.log("Unhandled webhook event type:", webhookEvent.type);
    }

    return new Response(
      JSON.stringify({ success: true }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Webhook processing error:", error);
    
    return new Response(
      JSON.stringify({ error: "Webhook processing failed" }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});

async function handlePaymentCreated(paymentData: SquarePaymentData) {
  console.log("Processing payment created:", paymentData);
  
  try {
    // Update payment record status to 'processing'
    const { error } = await supabase
      .from("payment_records")
      .update({
        status: "processing",
        square_payment_id: paymentData.id,
        updated_at: new Date().toISOString(),
      })
      .eq("payment_link_id", paymentData.order_id);

    if (error) {
      console.error("Error updating payment record:", error);
    } else {
      console.log("Payment record updated to processing");
    }
  } catch (error) {
    console.error("Error in handlePaymentCreated:", error);
  }
}

async function handlePaymentUpdated(paymentData: SquarePaymentData) {
  console.log("Processing payment updated:", paymentData);
  
  try {
    let status = "pending";
    
    // Map Square payment status to our status
    if (paymentData.status === "COMPLETED") {
      status = "completed";
    } else if (paymentData.status === "FAILED") {
      status = "failed";
    } else if (paymentData.status === "CANCELED") {
      status = "cancelled";
    }

    // Update payment record
    const { error } = await supabase
      .from("payment_records")
      .update({
        status,
        square_payment_id: paymentData.id,
        updated_at: new Date().toISOString(),
        metadata: {
          square_payment_data: paymentData,
          webhook_processed_at: new Date().toISOString(),
        },
      })
      .eq("payment_link_id", paymentData.order_id);

    if (error) {
      console.error("Error updating payment record:", error);
    } else {
      console.log(`Payment record updated to ${status}`);
      
      // If payment is completed, update booking status
      if (status === "completed") {
        await updateBookingStatus(paymentData.order_id);
      }
    }
  } catch (error) {
    console.error("Error in handlePaymentUpdated:", error);
  }
}

async function updateBookingStatus(paymentLinkId: string) {
  try {
    // Find the booking associated with this payment
    const { data: paymentRecord, error: paymentError } = await supabase
      .from("payment_records")
      .select("booking_id")
      .eq("payment_link_id", paymentLinkId)
      .single();

    if (paymentError || !paymentRecord?.booking_id) {
      console.log("No booking found for payment link:", paymentLinkId);
      return;
    }

    // Update booking status to confirmed
    const { error: bookingError } = await supabase
      .from("booking_forms")
      .update({
        status: "confirmed",
        payment_status: "paid",
        updated_at: new Date().toISOString(),
      })
      .eq("id", paymentRecord.booking_id);

    if (bookingError) {
      console.error("Error updating booking status:", bookingError);
    } else {
      console.log("Booking status updated to confirmed");
    }
  } catch (error) {
    console.error("Error updating booking status:", error);
  }
}