/**
 * Rate Limiter for Authentication
 * 
 * Implements client-side rate limiting to prevent brute force attacks
 * and excessive authentication attempts.
 */

interface RateLimitEntry {
  attempts: number;
  lastAttempt: number;
  blockedUntil?: number;
}

interface RateLimitConfig {
  maxAttempts: number;
  windowMs: number;
  blockDurationMs: number;
}

class AuthRateLimiter {
  private attempts: Map<string, RateLimitEntry> = new Map();
  private config: RateLimitConfig;

  constructor(config: Partial<RateLimitConfig> = {}) {
    this.config = {
      maxAttempts: config.maxAttempts || 5,
      windowMs: config.windowMs || 15 * 60 * 1000, // 15 minutes
      blockDurationMs: config.blockDurationMs || 30 * 60 * 1000, // 30 minutes
    };

    // Clean up old entries periodically
    setInterval(() => this.cleanup(), 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Check if an identifier (email/IP) is rate limited
   */
  isRateLimited(identifier: string): boolean {
    const entry = this.attempts.get(identifier);
    if (!entry) return false;

    const now = Date.now();

    // Check if still blocked
    if (entry.blockedUntil && now < entry.blockedUntil) {
      return true;
    }

    // Check if window has expired
    if (now - entry.lastAttempt > this.config.windowMs) {
      this.attempts.delete(identifier);
      return false;
    }

    return entry.attempts >= this.config.maxAttempts;
  }

  /**
   * Record a failed authentication attempt
   */
  recordFailedAttempt(identifier: string): void {
    const now = Date.now();
    const entry = this.attempts.get(identifier) || { attempts: 0, lastAttempt: 0 };

    // Reset if window has expired
    if (now - entry.lastAttempt > this.config.windowMs) {
      entry.attempts = 0;
    }

    entry.attempts++;
    entry.lastAttempt = now;

    // Block if max attempts reached
    if (entry.attempts >= this.config.maxAttempts) {
      entry.blockedUntil = now + this.config.blockDurationMs;
    }

    this.attempts.set(identifier, entry);
  }

  /**
   * Record a successful authentication (clears rate limit)
   */
  recordSuccessfulAttempt(identifier: string): void {
    this.attempts.delete(identifier);
  }

  /**
   * Get remaining attempts before rate limit
   */
  getRemainingAttempts(identifier: string): number {
    const entry = this.attempts.get(identifier);
    if (!entry) return this.config.maxAttempts;

    const now = Date.now();

    // Check if window has expired
    if (now - entry.lastAttempt > this.config.windowMs) {
      return this.config.maxAttempts;
    }

    return Math.max(0, this.config.maxAttempts - entry.attempts);
  }

  /**
   * Get time until unblocked (in milliseconds)
   */
  getTimeUntilUnblocked(identifier: string): number {
    const entry = this.attempts.get(identifier);
    if (!entry || !entry.blockedUntil) return 0;

    const now = Date.now();
    return Math.max(0, entry.blockedUntil - now);
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.attempts.entries()) {
      // Remove if window has expired and not blocked
      if (now - entry.lastAttempt > this.config.windowMs && 
          (!entry.blockedUntil || now > entry.blockedUntil)) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.attempts.delete(key));
  }

  /**
   * Get rate limit status for debugging
   */
  getStatus(identifier: string): {
    isBlocked: boolean;
    remainingAttempts: number;
    timeUntilUnblocked: number;
    totalAttempts: number;
  } {
    const entry = this.attempts.get(identifier);
    const isBlocked = this.isRateLimited(identifier);
    const remainingAttempts = this.getRemainingAttempts(identifier);
    const timeUntilUnblocked = this.getTimeUntilUnblocked(identifier);

    return {
      isBlocked,
      remainingAttempts,
      timeUntilUnblocked,
      totalAttempts: entry?.attempts || 0
    };
  }
}

// Export singleton instance
export const authRateLimiter = new AuthRateLimiter();

// Export error types
export class RateLimitError extends Error {
  constructor(
    message: string,
    public remainingTime: number,
    public remainingAttempts: number
  ) {
    super(message);
    this.name = 'RateLimitError';
  }
}

// Helper functions
export function checkRateLimit(identifier: string): void {
  if (authRateLimiter.isRateLimited(identifier)) {
    const timeUntilUnblocked = authRateLimiter.getTimeUntilUnblocked(identifier);
    const remainingAttempts = authRateLimiter.getRemainingAttempts(identifier);
    
    const minutes = Math.ceil(timeUntilUnblocked / (60 * 1000));
    throw new RateLimitError(
      `Too many failed attempts. Please try again in ${minutes} minute(s).`,
      timeUntilUnblocked,
      remainingAttempts
    );
  }
}

export function recordAuthAttempt(identifier: string, success: boolean): void {
  if (success) {
    authRateLimiter.recordSuccessfulAttempt(identifier);
  } else {
    authRateLimiter.recordFailedAttempt(identifier);
  }
}

export function getRateLimitStatus(identifier: string) {
  return authRateLimiter.getStatus(identifier);
}
