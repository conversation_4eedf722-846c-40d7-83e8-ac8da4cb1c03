/**
 * Security Test Suite
 * 
 * Comprehensive tests for security features including input validation,
 * authentication, authorization, and protection against common attacks.
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { InputSanitizer } from '../lib/utils/validation';
import { authRateLimiter, RateLimitError } from '../lib/auth/rateLimiter';
import { apiSecurity } from '../lib/api/security';
import { rbac, Permission, Role } from '../lib/auth/rbac';
import { ErrorHandler } from '../lib/utils/errorHandler';

describe('Input Sanitization', () => {
  describe('sanitizeHtml', () => {
    it('should remove script tags', () => {
      const malicious = '<script>alert("xss")</script>Hello';
      const sanitized = InputSanitizer.sanitizeHtml(malicious);
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).not.toContain('alert');
      expect(sanitized).toContain('Hello');
    });

    it('should escape HTML entities', () => {
      const input = '<div>Test & "quotes" \'apostrophes\'</div>';
      const sanitized = InputSanitizer.sanitizeHtml(input);
      expect(sanitized).toBe('&lt;div&gt;Test &amp; &quot;quotes&quot; &#x27;apostrophes&#x27;&lt;&#x2F;div&gt;');
    });

    it('should handle empty and null inputs', () => {
      expect(InputSanitizer.sanitizeHtml('')).toBe('');
      expect(InputSanitizer.sanitizeHtml(null as any)).toBe('');
      expect(InputSanitizer.sanitizeHtml(undefined as any)).toBe('');
    });
  });

  describe('sanitizeSql', () => {
    it('should escape single quotes', () => {
      const input = "'; DROP TABLE users; --";
      const sanitized = InputSanitizer.sanitizeSql(input);
      expect(sanitized).not.toContain("'; DROP");
      expect(sanitized).not.toContain('--');
    });

    it('should remove dangerous SQL keywords', () => {
      const input = 'SELECT * FROM users; DROP TABLE users;';
      const sanitized = InputSanitizer.sanitizeSql(input);
      expect(sanitized).not.toContain('DROP TABLE');
      expect(sanitized).not.toContain(';');
    });
  });

  describe('sanitizeEmail', () => {
    it('should normalize email addresses', () => {
      const input = '  <EMAIL>  ';
      const sanitized = InputSanitizer.sanitizeEmail(input);
      expect(sanitized).toBe('<EMAIL>');
    });

    it('should remove invalid characters', () => {
      const input = 'test<script>@example.com';
      const sanitized = InputSanitizer.sanitizeEmail(input);
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).toBe('<EMAIL>');
    });
  });
});

describe('Rate Limiting', () => {
  const testEmail = '<EMAIL>';

  beforeEach(() => {
    // Clear any existing rate limit data
    authRateLimiter.recordSuccessfulAttempt(testEmail);
  });

  it('should allow requests under the limit', () => {
    expect(() => {
      for (let i = 0; i < 4; i++) {
        authRateLimiter.recordFailedAttempt(testEmail);
      }
    }).not.toThrow();
    
    expect(authRateLimiter.isRateLimited(testEmail)).toBe(false);
  });

  it('should block requests over the limit', () => {
    // Exceed the rate limit
    for (let i = 0; i < 5; i++) {
      authRateLimiter.recordFailedAttempt(testEmail);
    }
    
    expect(authRateLimiter.isRateLimited(testEmail)).toBe(true);
  });

  it('should reset after successful attempt', () => {
    // Hit the rate limit
    for (let i = 0; i < 5; i++) {
      authRateLimiter.recordFailedAttempt(testEmail);
    }
    
    expect(authRateLimiter.isRateLimited(testEmail)).toBe(true);
    
    // Successful attempt should reset
    authRateLimiter.recordSuccessfulAttempt(testEmail);
    expect(authRateLimiter.isRateLimited(testEmail)).toBe(false);
  });
});

describe('API Security', () => {
  describe('Request Validation', () => {
    it('should detect suspicious payloads', async () => {
      const maliciousContext = {
        method: 'POST',
        url: '/api/test',
        headers: {},
        body: {
          name: '<script>alert("xss")</script>',
          comment: 'DROP TABLE users;'
        },
        timestamp: Date.now()
      };

      await expect(apiSecurity.validateRequest(maliciousContext)).rejects.toThrow();
    });

    it('should allow clean requests', async () => {
      const cleanContext = {
        method: 'POST',
        url: '/api/test',
        headers: { 'origin': 'http://localhost:3000' },
        body: {
          name: 'John Doe',
          email: '<EMAIL>',
          message: 'Hello world!'
        },
        timestamp: Date.now()
      };

      await expect(apiSecurity.validateRequest(cleanContext)).resolves.not.toThrow();
    });

    it('should validate request origins', async () => {
      const suspiciousContext = {
        method: 'POST',
        url: '/api/test',
        headers: { 'origin': 'https://malicious-site.com' },
        body: { test: 'data' },
        timestamp: Date.now()
      };

      await expect(apiSecurity.validateRequest(suspiciousContext)).rejects.toThrow();
    });
  });

  describe('CSRF Protection', () => {
    it('should generate valid CSRF tokens', () => {
      const userId = 'test-user-123';
      const token = apiSecurity.generateCSRFToken(userId);
      
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token.length).toBeGreaterThan(20);
    });

    it('should require CSRF tokens for state-changing operations', async () => {
      const context = {
        method: 'POST',
        url: '/api/test',
        headers: {},
        body: { test: 'data' },
        userId: 'test-user',
        timestamp: Date.now()
      };

      await expect(apiSecurity.validateRequest(context)).rejects.toThrow(/CSRF/);
    });
  });
});

describe('Role-Based Access Control', () => {
  const testUser = {
    id: 'user-123',
    email: '<EMAIL>',
    role: Role.CUSTOMER,
    isActive: true
  };

  const adminUser = {
    id: 'admin-123',
    email: '<EMAIL>',
    role: Role.ADMIN,
    isActive: true
  };

  describe('Permission Checking', () => {
    it('should allow users to access their own resources', () => {
      expect(rbac.hasPermission(testUser, Permission.VIEW_OWN_BOOKINGS)).toBe(true);
      expect(rbac.hasPermission(testUser, Permission.EDIT_OWN_BOOKINGS)).toBe(true);
    });

    it('should deny users access to admin functions', () => {
      expect(rbac.hasPermission(testUser, Permission.ACCESS_ADMIN_PANEL)).toBe(false);
      expect(rbac.hasPermission(testUser, Permission.VIEW_ALL_USERS)).toBe(false);
    });

    it('should allow admins to access admin functions', () => {
      expect(rbac.hasPermission(adminUser, Permission.ACCESS_ADMIN_PANEL)).toBe(true);
      expect(rbac.hasPermission(adminUser, Permission.VIEW_ALL_USERS)).toBe(true);
    });

    it('should deny access to inactive users', () => {
      const inactiveUser = { ...testUser, isActive: false };
      expect(rbac.hasPermission(inactiveUser, Permission.VIEW_OWN_BOOKINGS)).toBe(false);
    });
  });

  describe('Resource Access Control', () => {
    it('should allow users to access their own resources', () => {
      expect(rbac.canAccessResource(testUser, testUser.id)).toBe(true);
    });

    it('should deny users access to other users resources', () => {
      expect(rbac.canAccessResource(testUser, 'other-user-id')).toBe(false);
    });

    it('should allow admins to access any resource', () => {
      expect(rbac.canAccessResource(adminUser, 'any-user-id')).toBe(true);
    });
  });
});

describe('Error Handling Security', () => {
  describe('Sensitive Information Redaction', () => {
    it('should redact passwords from error details', () => {
      const error = ErrorHandler.createError('TEST_ERROR', 'Test error', {
        password: 'secret123',
        username: 'testuser',
        token: 'jwt-token-here'
      });

      expect(JSON.stringify(error.details)).not.toContain('secret123');
      expect(JSON.stringify(error.details)).not.toContain('jwt-token-here');
      expect(JSON.stringify(error.details)).toContain('[REDACTED]');
    });

    it('should redact JWT tokens from error messages', () => {
      const jwtToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
      const error = ErrorHandler.createError('TEST_ERROR', `Token error: ${jwtToken}`, {});

      expect(error.message).not.toContain(jwtToken);
      expect(error.message).toContain('[JWT_TOKEN_REDACTED]');
    });

    it('should partially redact email addresses', () => {
      const error = ErrorHandler.createError('TEST_ERROR', 'User <EMAIL> not found', {});
      
      expect(error.message).not.toContain('<EMAIL>');
      expect(error.message).toContain('test@[DOMAIN_REDACTED]');
    });
  });

  describe('Error Logging', () => {
    beforeEach(() => {
      ErrorHandler.clearErrorLogs();
    });

    it('should log errors securely', () => {
      const error = ErrorHandler.createError('TEST_ERROR', 'Test error', {
        sensitiveData: 'secret',
        publicData: 'public'
      });

      ErrorHandler.logError(error);
      
      const logs = ErrorHandler.getErrorLogs(10);
      expect(logs).toHaveLength(1);
      expect(logs[0].code).toBe('TEST_ERROR');
    });

    it('should maintain log size limits', () => {
      // Create more errors than the limit
      for (let i = 0; i < 60; i++) {
        const error = ErrorHandler.createError(`TEST_ERROR_${i}`, `Test error ${i}`, {});
        ErrorHandler.logError(error);
      }

      const logs = ErrorHandler.getErrorLogs(100);
      expect(logs.length).toBeLessThanOrEqual(50);
    });
  });
});

describe('Integration Security Tests', () => {
  it('should handle complete attack scenario', async () => {
    const attackPayload = {
      name: '<script>document.cookie="stolen=true"</script>',
      email: '<EMAIL>\'; DROP TABLE users; --',
      message: 'javascript:alert("xss")',
      password: 'password123',
      token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.malicious.token'
    };

    // Test input sanitization
    const sanitizedName = InputSanitizer.sanitizeHtml(attackPayload.name);
    expect(sanitizedName).not.toContain('<script>');

    const sanitizedEmail = InputSanitizer.sanitizeSql(attackPayload.email);
    expect(sanitizedEmail).not.toContain('DROP TABLE');

    // Test API security validation
    const maliciousContext = {
      method: 'POST',
      url: '/api/attack',
      headers: { 'origin': 'https://evil.com' },
      body: attackPayload,
      timestamp: Date.now()
    };

    await expect(apiSecurity.validateRequest(maliciousContext)).rejects.toThrow();

    // Test error handling doesn't leak sensitive info
    const error = ErrorHandler.createError('ATTACK_DETECTED', 'Attack detected', attackPayload);
    expect(JSON.stringify(error)).not.toContain('password123');
    expect(JSON.stringify(error)).not.toContain('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.malicious.token');
  });
});
